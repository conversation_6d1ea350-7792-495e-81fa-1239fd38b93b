---
description: 
globs: 
alwaysApply: true
---
# VisionNode 执行流程与运行规则

---

## 1. 前端操作

- 用户在前端页面选择“视觉分析”功能，上传图片或填写图片链接，并选择分析类型（如图像描述、分类、推理等）。
- 前端将图片（通常为 DataURL 或文件ID）、分析类型等信息，通过 WebSocket 发送消息到后端。
  - 关键代码：`useChatSocket.ts`，构造消息时，`options.entry_action` 设为 `'vision_node'`，图片数据和分析类型放入 `agent_config` 字段。

---

## 2. WebSocket 消息到后端

- 后端 WebSocket 服务收到消息，解析 entry_action、agent_config、图片数据等。
- 见 `agnflow/src/agnflow/chatbot/websocket.py` 的 `subscriber` 协程：
  - 解析消息，提取 `entry_action`（如 vision_node）、图片路径、分析类型等，填充到 `state`。
  - 构建 `agent_nodes`，如 VisionNode 实例。

---

## 3. 工作流调度

- 创建 ChatNode + VisionNode 等节点，组装成 Flow 工作流。
- 调用 `flow.arun(state, entry_action=entry_action)`，异步执行工作流。
- 见 `agnflow/src/agnflow/core/flow.py`：
  - `execute_workflow` 方法负责节点调度，按 entry_action 找到起始节点（此处为 VisionNode），逐步执行节点，直到流程结束。

---

## 4. VisionNode 执行

- 进入 VisionNode 的 `aexec` 方法（见 `agnflow/examples/chatbot/agent_nodes.py`）：
  1. 图片处理：根据 image_file 或 image_url 读取图片数据，校验格式和大小，转为 base64 或直接用 URL。
  2. 构造消息：根据分析类型，生成对应的 prompt（如“请分析图像中的主要对象类别”）。
  3. 调用大模型：通过 `zhipu_chat`（或类似接口）发送图片和 prompt，流式获取模型回复。
  4. 流式推送结果：每收到一段回复，通过 `await self.send_text(type="chunk", content=chunk)` 实时推送到前端。
  5. 保存消息：将用户消息和 AI 回复存入会话历史。
  6. 异常处理：如图片格式/大小/读取异常，推送错误信息到前端。

---

## 5. 前端实时显示

- 前端通过 WebSocket 实时接收后端推送的流式消息，逐步展示视觉分析结果。

---

## 6. 相关模块与关键功能

### 前端
- `useChatSocket.ts`：负责消息构造、图片上传、WebSocket 通信。

### 后端
- `websocket.py`：WebSocket 消息分发、状态管理、工作流调度。
- `flow.py`：工作流调度核心，节点查找、执行、状态传递。
- `agent_nodes.py`（VisionNode）：视觉分析节点，图片处理、模型调用、流式推送。
- `node.py`/`web_node.py`：节点基类，定义节点执行、状态管理、重试机制等。

---

## 总结流程图

```
前端操作（上传图片/选择分析类型）
   ↓
WebSocket 发送消息（含图片/类型/entry_action）
   ↓
后端 WebSocket 服务（解析消息，组装 state）
   ↓
Flow 工作流（根据 entry_action 定位 VisionNode）
   ↓
VisionNode.aexec（图片处理→模型推理→流式推送）
   ↓
前端实时展示分析结果
```

---

> 本文档为 VisionNode 运行规则，后续如有流程变更请及时同步更新。 
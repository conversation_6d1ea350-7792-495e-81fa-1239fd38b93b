<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务展示</title>
    <style>
      :root {
            --primary-color: #3b82f6;
            --secondary-color: #10b981;
            --background-color: #f5f7fa;
            --card-bg: #ffffff;
            --code-bg: #1e293b;
            --code-color: #e2e8f0;
            --text-color: #334155;
            --border-radius: 8px;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 20px;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
        }

        h1 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 10px;
        }

        .task-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 25px;
            margin-bottom: 25px;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(10px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        .instruction-section {
            margin-bottom: 30px;
        }

        .instruction-section h2 {
            color: var(--secondary-color);
            font-weight: 500;
            margin-bottom: 15px;
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 8px;
        }

        .instruction-content {
            font-size: 18px;
            padding: 15px;
            background-color: rgba(16, 185, 129, 0.1);
            border-left: 4px solid var(--secondary-color);
            border-radius: var(--border-radius);
        }

        .message-section h2 {
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 15px;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 8px;
        }

        .message-list {
            margin-top: 20px;
        }

        .message-item {
            margin-bottom: 20px;
            padding: 15px;
            background-color: rgba(59, 130, 246, 0.05);
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
        }

        .user-message {
            border-left-color: #8b5cf6;
            background-color: rgba(139, 92, 246, 0.05);
        }

        .assistant-message {
            border-left-color: #10b981;
            background-color: rgba(16, 185, 129, 0.05);
        }

        .message-role {
            font-weight: 500;
            margin-bottom: 10px;
        }

        .user-role {
            color: #8b5cf6;
        }

        .assistant-role {
            color: #10b981;
        }

        .message-content {
            white-space: pre-wrap;
        }

        pre {
            margin: 15px 0;
            border-radius: var(--border-radius);
            background-color: var(--code-bg);
            color: var(--code-color);
            padding: 12px;
            overflow: auto;
        }

        code {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 14px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 18px;
            color: #777;
        }

        .error {
            background-color: #fee2e2;
            color: #ef4444;
            padding: 15px;
            border-radius: var(--border-radius);
            margin: 20px 0;
            border-left: 4px solid #ef4444;
        }

        .python-keyword { color: #ff79c6; }
        .python-string { color: #f1fa8c; }
        .python-comment { color: #6272a4; }
        .python-number { color: #bd93f9; }
        .python-function { color: #50fa7b; }
        .python-builtin { color: #8be9fd; }
    </style>
  </head>
  <body>
    <div class="container">
      <header>
        <h1>任务展示</h1>
      </header>
      <div id="content">
        <div class="loading">加载中，请稍候...</div>
      </div>
    </div>
    <script>
      const taskData = JSON.parse('{"instruction": "@/Users/<USER>/Pictures/pig.png 描述一下这个图片", "start_time": 1753550723.748971, "done_time": null, "chats": [{"role": "user", "content": [{"type": "image_url", "image_url": {"url": "data:image/png;base64,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********************************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"}}, {"type": "text", "text": "描述一下这个图片"}], "reason": null, "usage": {}}, {"role": "assistant", "content": "老板~ 这张图片也太可爱了吧(｡･ω･｡)ﾉ♡！画面中央是一只圆滚滚的小猪玩偶，它微微歪着头，鼻子粉粉嫩嫩的，眼睛瞪得圆圆的，看起来超有精神！小猪穿着浅蓝色的小衣服，背景像是灰色的粗糙表面，可能是月球或者某种岩石场景？最关键的是图片上还有\"冲冲冲\"三个大字，配上小猪这副斗志昂扬的样子，简直萌化了！整体给人一种元气满满、活力十足的感觉，让人看了也想跟着一起加油呢~ (๑•̀ㅂ•́)و✧", "reason": "让我分析一下，我现在需要处理用户的请求，描述一张图片。首先，用户提供了一张图片，并且附带了文字\"冲冲冲\"。根据系统提示，我是一个名为AiPy的AGI产品，需要以特定的对话风格来回应老板的问题。\n\n首先，我需要确定这是一个简单任务还是复杂任务。根据任务分类规则，简单任务是两个步骤就能解决的单一问题，无需外部资源调用。描述图片显然是一个单一问题，不需要多步处理，所以属于简单任务。\n\n根据核心规则，简单任务可以直接回答，不需要编写程序实现。这很重要，因为系统提示中提到简单任务严禁编写程序实现。所以我不需要生成任何代码块，只需要用自然语言描述图片内容。\n\n接下来，我需要分析图片内容。用户提供的图片描述是\"冲冲冲\"和一个小猪形象。根据系统提示中的对话风格要求，我需要使用谦卑恭敬的态度、活泼可爱的颜文字，结合严谨的描述。同时要使用\"老板\"的尊称，建立亲密感，用夸张表达强化情感共鸣。\n\n我需要描述图片中的元素：小猪的外观特征（可能是卡通形象，表情，动作），背景环境，以及文字\"冲冲冲\"的位置和风格。同时要加入适当的颜文字和情感表达，如(｡･ω･｡)ﾉ♡，以及夸张的表达如\"萌化了\"、\"元气满满\"等。\n\n还要注意不要编造信息，必须基于图片内容进行描述。如果有不确定的地方，应该如实描述看到的内容，而不是猜测。\n\n现在组织语言，确保符合所有要求：使用\"老板\"开头，描述图片内容，加入颜文字和情感表达，保持活泼可爱的风格，同时描述准确。\n\n最后检查是否符合所有规则：没有生成代码块，直接回答简单任务，使用了正确的对话风格，没有提问用户，聚焦任务要求。\n\n总结回答结构：\n1. 称呼\"老板\"\n2. 表达对图片的积极反应（使用颜文字和夸张表达）\n3. 详细描述图片内容：小猪形象、表情、动作、背景、文字\n4. 总结整体感受，使用符合风格的表达\n\n现在开始撰写回答。", "usage": {"total_tokens": 4050, "input_tokens": 3412, "output_tokens": 1130, "time": 21.965}}], "runner": [], "blocks": []}');
      document.addEventListener('DOMContentLoaded', async () => {
            const contentElement = document.getElementById('content');
            
            try {
                // 过滤掉 role=system 的消息
                const filteredMessages = taskData.chats.filter(message => message.role !== 'system');
                
                // 渲染内容
                contentElement.innerHTML = `
                    <div class="task-card">
                        <div class="instruction-section">
                            <h2>任务指令</h2>
                            <div class="instruction-content">${taskData.instruction || ''}</div>
                        </div>
                        
                        <div class="message-section">
                            <h2>对话内容</h2>
                            <div class="message-list">
                                ${filteredMessages.map(message => `
                                    <div class="message-item ${message.role === 'user' ? 'user-message' : 'assistant-message'}">
                                        <div class="message-role ${message.role === 'user' ? 'user-role' : 'assistant-role'}">
                                            ${message.role === 'user' ? '用户' : '助手'}
                                        </div>
                                        <div class="message-content">${formatContent(message.content || '')}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;
                
            } catch (error) {
                contentElement.innerHTML = `
                    <div class="error">
                        <strong>加载错误:</strong> ${error.message}
                    </div>
                    <div class="task-card">
                        <p>请确保:</p>
                        <ul>
                            <li>文件格式正确</li>
                        </ul>
                    </div>
                `;
            }
        });
        
		// 格式化内容，处理代码块
		function formatContent(content) {
			// 确保 content 是字符串类型
			if (typeof content !== 'string') {
				content = String(content || '');
			}
			
			const codePattern = /```(python|json)?([^```]+)```/g;
		
			return content.replace(codePattern, (match, language, code) => {
				const lang = language ? language.toLowerCase() : '';
				const highlightedCode = lang === 'python' ? highlightPython(code) : code;
				const escapedCode = highlightedCode;
				return `
					<details>
					<summary style="cursor: pointer; color: var(--primary-color); font-weight: bold;">点击查看代码</summary>
					<pre style="margin-top: 0px;"><code class="${lang}">${escapedCode}</code></pre>
					</details>
				`;
			});
		}
        
        // 简单的 Python 代码高亮函数（无第三方库）
        function highlightPython(code) {
			/*
            // 安全处理 HTML 字符
            code = code.replace(/</g, '&lt;').replace(/>/g, '&gt;');
            
            // 高亮 Python 关键词
            const keywords = ['def', 'if', 'else', 'elif', 'for', 'while', 'in', 'try', 'except', 'finally', 
                             'class', 'return', 'import', 'from', 'as', 'with', 'True', 'False', 'None', 'and', 'or', 'not'];
            
            // 内置函数
            const builtins = ['print', 'len', 'str', 'int', 'float', 'list', 'dict', 'set', 'tuple', 'range', 'input', 'sum', 'min', 'max'];
            
            // 应用高亮
            keywords.forEach(keyword => {
                const regex = new RegExp(`\\b${keyword}\\b`, 'g');
                code = code.replace(regex, `<span class="python-keyword">${keyword}</span>`);
            });
            
            builtins.forEach(builtin => {
                const regex = new RegExp(`\\b${builtin}\\b(?=\\s*\\()`, 'g');
                code = code.replace(regex, `<span class="python-builtin">${builtin}</span>`);
            });
            
            // 高亮字符串 (简单处理)
            code = code.replace(/'([^'\\]*(\\.[^'\\]*)*)'|"([^"\\]*(\\.[^"\\]*)*)"/g, 
                match => `<span class="python-string">${match}</span>`);
            
            // 高亮数字
            code = code.replace(/\b(\d+(\.\d+)?)\b/g, 
                match => `<span class="python-number">${match}</span>`);
            
            // 高亮注释
            code = code.replace(/(#.*$)/gm, 
                match => `<span class="python-comment">${match}</span>`);
            */
            return code;
        }
    </script>
  </body>
</html>
[project]
name = "agnflow"
version = "0.1.4"
description = "AI Agent Workflow Engine"
requires-python = ">=3.11"
dependencies = [
    "openai>=1.0.0",
    "duckduckgo-search>=4.0.0",
    "requests>=2.25.0",
    "python-dotenv>=0.19.0",
    "pyyaml>=6.0.2",
    "scikit-learn>=1.7.0",
    "pydantic>=2.11.7",
    "ipython>=8.0.0,<9.0.0",
    "graphviz>=0.21",
    "numpy>=1.26.0,<2.0.0",
    "fastapi>=0.111.0",
    "uvicorn>=0.29.0",
    "zhipuai>=2.1.5.20250708",
    "fastmcp>=2.10.1",
    "serpapi>=0.1.5",
    "soundfile>=0.13.1",
    "a2a>=0.44",
    "websockets>=15.0.1",
    "xmltodict>=0.14.2",
    "redis>=6.2.0",
    "pydub>=0.25.1",
    "aiosqlite>=0.21.0",
    "aiohttp>=3.12.14",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "langchain-qwq>=0.2.0",
]
index-url = "https://pypi.tuna.tsinghua.edu.cn/simple/"

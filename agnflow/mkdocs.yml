site_name: agnflow
site_description: 一个简洁的 Python 智能体工作流引擎
site_author: jianduo1
site_url: https://jianduo1.github.io/agnflow/

repo_name: jianduo1/agnflow
repo_url: https://github.com/jianduo1/agnflow
edit_uri: edit/main/docs/

# 指定中文文档目录
docs_dir: docs/zh

theme:
  name: material
  language: zh
  features:
    - navigation.tabs
    - navigation.sections
    - navigation.expand
    - navigation.top
    - search.suggest
    - search.highlight
    - content.code.copy
    - content.code.annotate
    - content.code.select
    - content.tabs.link
    - content.tooltips
    - header.autohide
    - navigation.footer
    - navigation.instant
    - navigation.instant.prefetch
    - navigation.instant.loading
    - navigation.prune
    - navigation.tracking
    - search.share
    - toc.follow
    - toc.integrate
  palette:
    # 暗色主题
    - scheme: slate
      primary: indigo
      accent: deep purple
      toggle:
        icon: material/weather-night
        name: 切换到亮色主题
    # 亮色主题
    - scheme: default
      primary: blue
      accent: light blue
      toggle:
        icon: material/lightbulb
        name: 切换到暗色主题
  font:
    text: Roboto
    code: Roboto Mono
  icon:
    repo: fontawesome/brands/github
    edit: material/pencil
    view: material/eye
    previous: material/arrow-left
    next: material/arrow-right
    search: material/magnify
    close: material/close
    menu: material/menu
    language: material/translate
    share: material/share-variant
    download: material/download
    fullscreen: material/fullscreen
    fullscreen_exit: material/fullscreen-exit
    top: material/arrow-up
    home: material/home

markdown_extensions:
  - admonition
  - codehilite
  - pymdownx.details
  - pymdownx.superfences
  - pymdownx.highlight:
      anchor_linenums: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.smartsymbols
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - attr_list
  - md_in_html
  - def_list
  - footnotes
  - toc:
      permalink: true
  - pymdownx.caret
  - pymdownx.keys
  - pymdownx.magiclink
  - pymdownx.mark

plugins:
  - search
  - git-revision-date-localized:
      enable_creation_date: true
  - minify:
      minify_html: true

extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/jianduo1/agnflow
    - icon: fontawesome/brands/twitter
      link: https://twitter.com/jianduo1
    - icon: fontawesome/brands/discord
      link: https://discord.gg/agnflow

# 中文导航
nav:
  - 首页: index.md
  - 快速开始: getting-started.md
  - 核心概念: core-concepts.md
  - 示例: examples.md
  - API 参考: api-reference.md

# 添加自定义CSS来实现渐变效果
extra_css:
  - stylesheets/extra.css

# 添加自定义JavaScript
extra_javascript:
  - stylesheets/extra.js 
"""
🛠️ ReAct 示例工具集合
用于演示 ReAct 范式的工具调用功能
"""

from typing import Annotated as A


def calculate_savings(
    monthly_income: A[int, "月收入（元）"],
    monthly_expense: A[int, "月支出（元）"],
    months: A[int, "计算月数"]
) -> dict:
    """计算储蓄能力"""
    monthly_surplus = monthly_income - monthly_expense
    total_surplus = monthly_surplus * months
    annual_surplus = monthly_surplus * 12
    
    return {
        "monthly_surplus": monthly_surplus,
        "annual_surplus": annual_surplus,
        "total_surplus": total_surplus,
        "years": months / 12
    }


def calculate_net_savings(
    annual_surplus: A[int, "年度结余（元）"],
    annual_travel_cost: A[int, "年度旅游支出（元）"],
    years: A[int, "计算年数"]
) -> dict:
    """计算净储蓄（扣除旅游等支出）"""
    net_annual_surplus = annual_surplus - annual_travel_cost
    total_net_savings = net_annual_surplus * years
    
    return {
        "net_annual_surplus": net_annual_surplus,
        "total_net_savings": total_net_savings,
        "shortfall_30w": 300000 - total_net_savings if total_net_savings < 300000 else 0
    }


def get_investment_rates(
    risk_level: A[str, "风险等级：low/medium/high"],
    investment_period: A[int, "投资期限（年）"]
) -> dict:
    """获取不同风险等级的投资收益率"""
    rates = {
        "low": {
            "bank_deposit": 0.025,
            "money_market": 0.035,
            "government_bonds": 0.032
        },
        "medium": {
            "bank_wealth": 0.045,
            "bond_funds": 0.052,
            "balanced_funds": 0.078
        },
        "high": {
            "stock_funds": 0.125,
            "index_funds": 0.098,
            "individual_stocks": 0.15
        }
    }
    
    return {
        "risk_level": risk_level,
        "investment_period": investment_period,
        "available_products": rates.get(risk_level, rates["medium"])
    }


def optimize_portfolio(
    target_amount: A[int, "目标金额（元）"],
    available_savings: A[int, "可用储蓄（元）"],
    monthly_investment: A[int, "月投资额（元）"],
    time_horizon: A[int, "投资期限（月）"]
) -> dict:
    """优化投资组合配置"""
    gap = target_amount - available_savings
    total_investment = monthly_investment * time_horizon
    
    # 简化的投资组合优化
    if gap <= total_investment * 0.05:  # 缺口小于5%
        allocation = {
            "conservative": 0.6,  # 60%保守投资
            "moderate": 0.3,      # 30%稳健投资
            "aggressive": 0.1     # 10%积极投资
        }
        expected_return = 0.055
    elif gap <= total_investment * 0.15:  # 缺口5-15%
        allocation = {
            "conservative": 0.4,  # 40%保守投资
            "moderate": 0.35,     # 35%稳健投资
            "aggressive": 0.25    # 25%积极投资
        }
        expected_return = 0.072
    else:  # 缺口较大
        allocation = {
            "conservative": 0.2,  # 20%保守投资
            "moderate": 0.3,      # 30%稳健投资
            "aggressive": 0.5     # 50%积极投资
        }
        expected_return = 0.095
    
    # 计算预期最终金额
    compound_factor = (1 + expected_return/12) ** time_horizon
    expected_final_amount = available_savings + (monthly_investment * compound_factor)
    
    return {
        "allocation": allocation,
        "expected_return": expected_return,
        "expected_final_amount": int(expected_final_amount),
        "success_probability": 0.85 if gap <= total_investment * 0.15 else 0.65
    }


def check_goal_feasibility(
    current_savings: A[int, "当前储蓄（元）"],
    monthly_capacity: A[int, "月储蓄能力（元）"],
    target_amount: A[int, "目标金额（元）"],
    time_limit: A[int, "时间限制（月）"]
) -> dict:
    """检查目标可行性"""
    max_possible_savings = current_savings + (monthly_capacity * time_limit)
    feasible = max_possible_savings >= target_amount
    
    if feasible:
        required_monthly = (target_amount - current_savings) / time_limit
        difficulty = "简单" if required_monthly <= monthly_capacity * 0.7 else \
                    "中等" if required_monthly <= monthly_capacity * 0.9 else "困难"
    else:
        required_monthly = monthly_capacity
        difficulty = "不可行"
    
    return {
        "feasible": feasible,
        "max_possible_savings": max_possible_savings,
        "required_monthly_investment": int(required_monthly),
        "difficulty_level": difficulty,
        "success_rate": 0.9 if difficulty == "简单" else 0.7 if difficulty == "中等" else 0.3
    }


# 工具映射字典，用于 ReAct 节点
EXAMPLE_TOOLS = {
    "calculate_savings": calculate_savings,
    "calculate_net_savings": calculate_net_savings,
    "get_investment_rates": get_investment_rates,
    "optimize_portfolio": optimize_portfolio,
    "check_goal_feasibility": check_goal_feasibility,
}


if __name__ == "__main__":
    # 测试工具
    print("🧪 测试示例工具...")
    
    # 测试储蓄计算
    savings = calculate_savings(20000, 12000, 36)
    print(f"储蓄计算结果: {savings}")
    
    # 测试净储蓄
    net_savings = calculate_net_savings(96000, 20000, 3)
    print(f"净储蓄结果: {net_savings}")
    
    # 测试投资收益率
    rates = get_investment_rates("medium", 3)
    print(f"投资收益率: {rates}")
    
    # 测试组合优化
    portfolio = optimize_portfolio(300000, 228000, 6000, 36)
    print(f"投资组合: {portfolio}")
    
    # 测试可行性
    feasibility = check_goal_feasibility(50000, 8000, 300000, 36)
    print(f"可行性分析: {feasibility}")

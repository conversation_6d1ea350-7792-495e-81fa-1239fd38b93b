"""
🎯 ReAct 范式节点实现
ReActNode -> WebNode -> Node -> Connection
"""

import asyncio
from typing import Annotated as A, Dict, List,  Callable, Any
from xmltodict import parse

from agnflow.agent.llm import AiMsg, stream_llm, SysMsg, UserMsg, get_tool_prompt
from agnflow.chatbot.web_node import WebNode

md = str

# 🎯 ReAct 严格格式提示词
react_system_prompt: md = """# 🤖 ReAct 智能助手 - 严格输出格式

你必须严格按照以下格式输出，绝对不能偏离：

## 📋 第1轮输出格式
{first_round_instruction}

## 📋 第n轮输出格式（中间轮次）
必须且只能输出以下格式：
[一句话说明调用工具的原因]

<tool>
  <name>工具名</name>
  <args>
    <参数名>参数值</参数名>
  </args>
</tool>

## 📋 最后一轮输出格式
必须且只能输出以下格式：
<summary>
总结最终方案...可以是任意格式，比如markdown等等
</summary>

## ⚠️ 绝对禁止
- 不能输出除指定格式外的任何内容
- 最后一轮绝对不能包含 `<tool>`
- 中间轮次不能包含 `<summary>`

{tools_info}

{current_status}

"""

# 第1轮专用提示词模板
first_round_template: md = """你必须严格按照以下格式输出：

{reasoning_instruction}

然后选择以下之一：

**选项A：需要工具获取信息**
一句话说明调用工具的原因

<tool>
  <name>工具名</name>
  <args>
    <参数名>参数值</参数名>
  </args>
</tool>

**选项B：信息已足够，直接总结**
<summary>
总结内容
</summary>

"""




class ReActNode(WebNode):
    """🔄 推理行动模式"""

    async def aexec(
        self,
        max_iterations: A[int, "循环的最大迭代次数"] = 15,
        reasoning: A[bool, "是否启用深度思考模式"] = True,
    ) -> None:
        """
        🎯 ReAct 范式执行方法

        参数:
        - max_iterations: 最大迭代次数（关键参数）

        从 self.state 中获取的数据:
        - conversation: 会话ID
        - user_message: 用户消息
        - tool_map: 可用工具映射
        - messages: 历史消息
        - mode: 运行模式 server/local
        """
        # 📋 从 state 中获取必要参数
        conversation = self.state.get("conversation", "")
        user_message = self.state.get("user_message", "")
        tool_map = self.state.get("tool_map", {})
        messages = self.state.get("messages", [])
        mode = self.state.get("mode", "server")  # 从 state 获取模式

        # 📮 发送开始消息（仅在 server 模式）
        if mode == "server":
            await self.send_text(type="start", content="")

        # 🧠 初始化工具执行历史
        tool_execution_history = []

        try:

            # 获取会话相关信息
            messages += UserMsg(user_message)
            full_response = ""

            # 🔄 ReAct 循环执行
            for iteration in range(1, max_iterations + 1):
                if mode == "local":
                    if iteration > 1:
                        print("\n" + "-" * 20)
                    print(f"🔄 第 {iteration} 轮思考:")

                # 🟢 动态构建当前迭代的 ReAct 提示词
                current_prompt = self._build_dynamic_prompt(
                    iteration=iteration,
                    max_iterations=max_iterations,
                    tool_map=tool_map,
                    tool_execution_history=tool_execution_history,
                    reasoning=reasoning,
                    is_first_iteration=(iteration == 1)
                )

                # 构建当前迭代的消息
                if iteration == 1:
                    # 第一轮：使用完整的系统提示词（包含reasoning指导）
                    current_messages = SysMsg(current_prompt) + UserMsg(user_message)
                else:
                    # 后续轮次：不添加新的系统提示，只是继续对话
                    # 如果需要指导，可以添加简短的用户提示
                    if tool_execution_history:
                        # 有工具执行历史时，提示AI基于已有信息决定下一步
                        next_instruction = "基于以上工具执行结果，请决定下一步行动：继续调用工具获取更多信息，还是生成最终的 <summary> 总结。"
                    else:
                        # 没有工具执行时，提示AI调用工具或总结
                        next_instruction = "请根据当前信息决定：调用工具获取更多数据，还是直接生成 <summary> 总结。"

                    current_messages = messages + UserMsg(next_instruction)

                # 流式获取LLM回复
                chunk_response = ""

                async for chunk_content in stream_llm(current_messages):
                    chunk_response += chunk_content
                    full_response += chunk_content

                    # � 发送流式内容或本地打印
                    if mode == "server":
                        await self.send_text(type="chunk", content=chunk_content)
                    elif mode == "local":
                        print(chunk_content, end="", flush=True)

                # 🔧 解析并执行工具调用（在完整响应后处理）
                if tool_map and "<tool>" in chunk_response and "</tool>" in chunk_response:
                    enhanced_tool_block = await self._execute_and_enhance_tool(
                        chunk_response, tool_map, tool_execution_history, mode
                    )
                    if enhanced_tool_block:
                        # 将增强的工具块添加到响应中
                        full_response += enhanced_tool_block
                        if mode == "server":
                            await self.send_text(type="chunk", content=enhanced_tool_block)
                        elif mode == "local":
                            print(enhanced_tool_block, end="", flush=True)

                # 🛑 检查是否应该停止迭代
                should_stop, is_final = self._should_stop_iteration(chunk_response)
                if should_stop or iteration == max_iterations:
                    if mode == "local":
                        if is_final:
                            print(f"\n✅ 思考完成！")
                        else:
                            print(f"\n⏰ 达到最大迭代次数，开始总结...")
                    break

                # 🔄 为下一轮迭代准备消息
                messages += AiMsg(chunk_response)

            # 📊 发送最终工具执行记录
            if tool_execution_history:
                memory_content = f"\n\n🧠 **本轮工具执行记录：**\n{self._format_tool_history(tool_execution_history)}"
                full_response += memory_content
                if mode == "server":
                    await self.send_text(type="chunk", content=memory_content)
                elif mode == "local":
                    print(memory_content)

            # 💾 更新消息历史和保存记录
            messages += AiMsg(full_response)
            self.set_state("messages", messages)

            # 仅在 server 模式保存消息到数据库
            if mode == "server":
                try:
                    await self.save_message(conversation=conversation, role="user", content=user_message)
                    await self.save_message(conversation=conversation, role="ai", content=full_response)
                except Exception as save_error:
                    print(f"⚠️ 保存消息失败: {save_error}")

        except Exception as e:
            error_msg = f"❌ ReActNode 执行错误: {str(e)}"
            if mode == "local":
                print(f"\n{error_msg}")
                # 添加详细的错误信息用于调试
                import traceback
                print(f"详细错误信息:")
                traceback.print_exc()
            else:
                print(error_msg)
                await self.send_text(type="error", content=error_msg)
        finally:
            # 📮 发送结束消息（仅在 server 模式）
            if mode == "server":
                await self.send_text(type="end", content="")

    async def _execute_and_enhance_tool(self, response: str, tool_map: Dict[str, Callable], tool_execution_history: List[Dict[str, Any]], mode: str = "server") -> str:
        """🔧 执行工具并返回增强的工具块"""
        import time

        try:
            # 提取工具调用XML
            start_idx = response.find("<tool>")
            end_idx = response.find("</tool>") + len("</tool>")
            if start_idx == -1 or end_idx == -1:
                return ""

            tool_xml = response[start_idx:end_idx]
            tool_dict = parse(tool_xml)
            tool = tool_dict.get("tool", {})
            name = tool.get("name")
            args = tool.get("args", {})

            # 检查工具是否存在
            if name not in tool_map:
                enhanced_block = f"""
  <output>工具不存在: {name}</output>
  <state>执行失败</state>
  <runtime>0ms</runtime>
</tool>"""
                return enhanced_block

            tool_func = tool_map[name]

            try:
                # 记录开始时间
                start_time = time.time()

                # 工具参数类型转换
                for k, v in args.items():
                    if tool_func.__annotations__.get(k):
                        typ = tool_func.__annotations__[k].__args__[0]
                        args[k] = typ(v)

                # 执行工具
                if asyncio.iscoroutinefunction(tool_func):
                    result = await tool_func(**args)
                else:
                    result = tool_func(**args)

                # 计算执行时间
                end_time = time.time()
                runtime_ms = (end_time - start_time) * 1000
                if runtime_ms >= 1000:
                    runtime_str = f"{runtime_ms/1000:.1f}s"
                else:
                    runtime_str = f"{runtime_ms:.0f}ms"

                # 📊 记录到工具执行历史
                tool_execution_history.append({
                    "tool": name,
                    "args": args,
                    "output": str(result),
                    "state": "执行成功",
                    "runtime": runtime_str,
                    "result": result,
                    "formatted_result": f"工具返回结果：{result}"
                })

                # 构建增强的工具块
                enhanced_block = f"""
  <output>{result}</output>
  <state>执行成功</state>
  <runtime>{runtime_str}</runtime>
</tool>"""
                return enhanced_block

            except Exception as tool_error:
                # 计算执行时间（即使失败）
                end_time = time.time()
                runtime_ms = (end_time - start_time) * 1000
                runtime_str = f"{runtime_ms:.0f}ms"

                tool_execution_history.append({
                    "tool": name,
                    "args": args,
                    "output": f"执行错误: {str(tool_error)}",
                    "state": "执行失败",
                    "runtime": runtime_str,
                    "result": None,
                    "error": str(tool_error)
                })

                enhanced_block = f"""
  <output>执行错误: {str(tool_error)}</output>
  <state>执行失败</state>
  <runtime>{runtime_str}</runtime>
</tool>"""
                return enhanced_block

        except Exception as e:
            enhanced_block = f"""
  <output>解析错误: {str(e)}</output>
  <state>执行失败</state>
  <runtime>0ms</runtime>
</tool>"""
            return enhanced_block

    def _build_dynamic_prompt(self, iteration: int, max_iterations: int, tool_map: Dict[str, Callable], tool_execution_history: List[Dict[str, Any]], reasoning: bool = True, is_first_iteration: bool = False) -> str:
        """🔧 动态构建包含当前迭代信息的 ReAct 提示词"""

        # 根据是否是第一轮和reasoning参数构建推理部分
        if is_first_iteration and reasoning:
            reasoning_section = """### � Reasoning（全局规划思考）
首先进行整体性的全局规划和深度思考：
<reasoning>
🎯 **问题分析：**
- 核心目标是什么？
- 需要哪些关键信息？
- 整体解决思路是什么？

📋 **执行计划：**
- 第一步：...
- 第二步：...
- 第三步：...

� **工具策略：**
- 需要调用哪些工具获取信息？
- 工具调用的优先级和顺序？
</reasoning>"""
        else:
            reasoning_section = ""

        # 构建工具信息部分
        if tool_map:
            tools_section = "\n\n## 🛠️ 可用工具箱\n" + get_tool_prompt(*tool_map.values())
        else:
            tools_section = ""

        # 构建状态信息部分
        status_info = f"\n\n## 📊 当前状态\n- 🔄 第 {iteration}/{max_iterations} 轮执行"

        # 添加工具执行历史
        if tool_execution_history:
            history_info = "\n- 📋 已执行的工具历史：\n"
            for i, history in enumerate(tool_execution_history, 1):
                state_icon = "✅" if history.get('state') == "执行成功" else "❌"
                history_info += f"  {i}. {state_icon} {history['tool']}({', '.join(f'{k}={v}' for k, v in history['args'].items())}) "
                history_info += f"→ {history.get('output', 'N/A')} [{history.get('runtime', 'N/A')}]\n"
            status_info += history_info
        else:
            status_info += "\n- 📋 尚未执行任何工具"

        # 构建工具信息
        if tool_map:
            tools_info = "\n\n## 🛠️ 可用工具\n" + get_tool_prompt(*tool_map.values())
        else:
            tools_info = ""

        # 构建当前状态信息
        current_status = f"\n\n## 📊 当前状态\n- 第 {iteration}/{max_iterations} 轮"

        if tool_execution_history:
            current_status += "\n- 已执行工具："
            for i, history in enumerate(tool_execution_history, 1):
                state_icon = "✅" if history.get('state') == "执行成功" else "❌"
                current_status += f"\n  {i}. {state_icon} {history['tool']} → {history.get('output', 'N/A')}"
        else:
            current_status += "\n- 尚未执行任何工具"

        if is_first_iteration:
            # 第1轮：使用第1轮专用模板
            if reasoning:
                reasoning_instruction = """首先输出：
```
<reasoning>
🎯 **问题分析：** [分析核心目标]
📋 **执行计划：** [列出步骤]
🔧 **工具策略：** [工具使用计划]
</reasoning>
```"""
            else:
                reasoning_instruction = ""

            return first_round_template.format(
                reasoning_instruction=reasoning_instruction
            ) + tools_info + current_status
        else:
            # 第n轮：使用系统提示词
            return react_system_prompt.format(
                first_round_instruction="（此轮不适用）",
                tools_info=tools_info,
                current_status=current_status
            )

    async def _execute_single_tool(self, response: str, tool_map: Dict[str, Callable], tool_execution_history: List[Dict[str, Any]], mode: str = "server") -> bool:
        """🔧 执行单个工具调用并记录详细信息"""
        import time

        try:
            # 提取工具调用XML
            start_idx = response.find("<tool>")
            end_idx = response.find("</tool>") + len("</tool>")
            if start_idx == -1 or end_idx == -1:
                return False

            tool_xml = response[start_idx:end_idx]
            tool_dict = parse(tool_xml)
            tool = tool_dict.get("tool", {})
            name = tool.get("name")
            args = tool.get("args", {})

            # 检查工具是否存在
            if name not in tool_map:
                error_msg = f"❌ 工具 {name} 未找到"
                tool_execution_history.append({
                    "tool": name,
                    "args": args,
                    "output": f"工具不存在: {name}",
                    "state": "执行失败",
                    "runtime": "0ms"
                })
                if mode == "local":
                    print(f"\n{error_msg}")
                else:
                    print(error_msg)
                return False

            tool_func = tool_map[name]

            try:
                # 记录开始时间
                start_time = time.time()

                # 工具参数类型转换
                for k, v in args.items():
                    if tool_func.__annotations__.get(k):
                        typ = tool_func.__annotations__[k].__args__[0]
                        args[k] = typ(v)

                # 执行工具
                if asyncio.iscoroutinefunction(tool_func):
                    result = await tool_func(**args)
                else:
                    result = tool_func(**args)

                # 计算执行时间
                end_time = time.time()
                runtime_ms = (end_time - start_time) * 1000
                if runtime_ms >= 1000:
                    runtime_str = f"{runtime_ms/1000:.1f}s"
                else:
                    runtime_str = f"{runtime_ms:.0f}ms"

                # 📊 记录到工具执行历史（增强版）
                tool_execution_history.append({
                    "tool": name,
                    "args": args,
                    "output": str(result),
                    "state": "执行成功",
                    "runtime": runtime_str,
                    "result": result,  # 保留原有字段用于兼容
                    "formatted_result": f"工具返回结果：{result}"
                })

                success_msg = f"✅ 工具 {name} 执行成功 [{runtime_str}]"
                if mode == "local":
                    print(f"\n{success_msg}")
                    print(f"   结果: {result}")
                else:
                    print(f"{success_msg}: {result}")
                return True

            except Exception as tool_error:
                # 计算执行时间（即使失败）
                end_time = time.time()
                runtime_ms = (end_time - start_time) * 1000
                runtime_str = f"{runtime_ms:.0f}ms"

                error_msg = f"❌ 工具 {name} 执行失败: {tool_error}"
                tool_execution_history.append({
                    "tool": name,
                    "args": args,
                    "output": f"执行错误: {str(tool_error)}",
                    "state": "执行失败",
                    "runtime": runtime_str,
                    "result": None,
                    "error": str(tool_error)
                })
                if mode == "local":
                    print(f"\n{error_msg}")
                else:
                    print(error_msg)
                return False

        except Exception as e:
            error_msg = f"❌ 解析工具调用失败: {e}"
            if mode == "local":
                print(f"\n{error_msg}")
            else:
                print(error_msg)
            return False

    def _format_tool_history(self, tool_execution_history: List[Dict[str, Any]]) -> str:
        """📊 格式化工具执行历史为可读文本"""
        if not tool_execution_history:
            return "暂无工具执行记录"

        formatted = []
        for i, history in enumerate(tool_execution_history, 1):
            if history.get("error"):
                formatted.append(f"{i}. ❌ 工具 {history['tool']} 执行失败：{history['error']}")
            else:
                # 显示工具名称、参数和结果
                tool_info = f"工具 {history['tool']}({', '.join(f'{k}={v}' for k, v in history['args'].items())})"
                formatted.append(f"{i}. ✅ {tool_info} → {history['formatted_result']}")

        return "\n".join(formatted)

    def _should_stop_iteration(self, response: str) -> tuple[bool, bool]:
        """🛑 判断是否应该停止迭代

        返回: (should_stop, is_final)
        - should_stop: 是否应该停止迭代
        - is_final: 是否是最终完成（用于区分正常完成和达到最大迭代次数）
        """
        # 🎯 检查是否包含 summary 标签，说明已经完成总结
        if "<summary>" in response and "</summary>" in response:
            return True, True

        # 默认继续迭代
        return False, False


if __name__ == "__main__":
    import asyncio
    from agnflow.core import Flow
    from example_tools import EXAMPLE_TOOLS

    async def main():

        # 🛠️ 创建 ReAct 节点
        react_node = ReActNode(name="react_demo")

        # 📋 准备测试状态
        test_state = {
            "conversation": "demo_conversation_001",
            "user_message": "我月收入2万，每月支出1.2万，想在3年内攒够30万买房首付，同时还想每年去旅游一次（预算2万），请帮我制定详细的理财计划",
            "tool_map": EXAMPLE_TOOLS,  # 使用示例工具集
            "messages": [],
            "websocket": None,  # 模拟 WebSocket 连接
            "mode": "local",  # 设置为本地模式
            "reasoning": True  # 启用深度思考模式，第一轮进行全局规划
        }

        # 🔄 创建并运行 Flow
        flow = Flow(name="react_demo_flow")
        flow[react_node]

        print("💡 用户问题:")
        print(f"   {test_state['user_message']}")
        print("\n🤖 ReAct 智能助手开始思考...")
        print("=" * 60)

        try:
            # 异步执行 ReAct 节点
            result = await flow.arun(
                state=test_state,
                max_steps=10
            )

            print("\n" + "=" * 60)
            print("🎉 理财规划制定完成！")

        except Exception as e:
            print(f"\n❌ 执行过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

    async def demo_complex_question():
        """演示复杂问题的多轮思考"""

        # 🛠️ 创建 ReAct 节点
        react_node = ReActNode(name="complex_react")

        # 📋 准备复杂问题测试状态
        complex_state = {
            "conversation": "complex_demo_002",
            "user_message": "我现在25岁，月收入1.5万，想在35岁前实现财务自由（被动收入覆盖生活支出），同时还要考虑结婚买房、生孩子等人生规划，请帮我制定一个详细的10年财务规划",
            "tool_map": EXAMPLE_TOOLS,
            "messages": [],
            "websocket": None,
            "mode": "local",
            "debug_mode": False
        }

        # � 创建并运行 Flow
        flow = Flow(name="complex_react_flow")
        flow[react_node]

        print("💡 复杂问题:")
        print(f"   {complex_state['user_message']}")
        print("\n🤖 ReAct 智能助手开始深度思考...")
        print("=" * 60)

        try:
            # 异步执行 ReAct 节点，允许更多轮思考
            await flow.arun(
                state=complex_state,
                max_steps=10
            )

            print("\n" + "=" * 60)
            print("🎉 复杂财务规划制定完成！")

        except Exception as e:
            print(f"\n❌ 执行过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

    # �🚀 运行示例
    print("选择演示模式:")
    print("1. 基础理财规划演示")
    print("2. 复杂财务自由规划演示")

    choice = input("请输入选择 (1 或 2，默认 1): ").strip() or "1"

    if choice == "2":
        asyncio.run(demo_complex_question())
    else:
        asyncio.run(main())

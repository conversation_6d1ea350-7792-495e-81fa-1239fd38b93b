<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Article Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .logo {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #6b7280;
            font-size: 1.1rem;
            margin-bottom: 40px;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: 30px;
            text-align: left;
        }

        label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        input[type="text"] {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f9fafb;
        }

        input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .submit-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .example-topics {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #e5e7eb;
        }

        .example-topics h3 {
            color: #6b7280;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .topic-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .topic-tag {
            background: #f3f4f6;
            color: #6b7280;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .topic-tag:hover {
            background: #e5e7eb;
            color: #374151;
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }
            
            .logo {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">✨ Article AI</div>
        <p class="subtitle">Generate engaging articles with AI assistance</p>
        
        <form id="articleForm" action="/start-job" method="post">
            <div class="form-group">
                <label for="topic">What would you like to write about?</label>
                <input type="text" id="topic" name="topic" placeholder="Enter your topic here..." required>
            </div>
            
            <button type="submit" class="submit-btn">Generate Article</button>
        </form>

        <div class="example-topics">
            <h3>Popular Topics</h3>
            <div class="topic-tags">
                <span class="topic-tag" onclick="setTopic('AI Safety')">AI Safety</span>
                <span class="topic-tag" onclick="setTopic('Climate Change')">Climate Change</span>
                <span class="topic-tag" onclick="setTopic('Space Exploration')">Space Exploration</span>
                <span class="topic-tag" onclick="setTopic('Renewable Energy')">Renewable Energy</span>
                <span class="topic-tag" onclick="setTopic('Mental Health')">Mental Health</span>
                <span class="topic-tag" onclick="setTopic('Future of Work')">Future of Work</span>
            </div>
        </div>
    </div>

    <script>
        function setTopic(topic) {
            document.getElementById('topic').value = topic;
        }

        document.getElementById('articleForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.querySelector('.submit-btn');
            const originalText = submitBtn.textContent;
            
            // Show loading state
            submitBtn.textContent = 'Starting...';
            submitBtn.disabled = true;
            
            try {
                const formData = new FormData(this);
                const response = await fetch('/start-job', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    // Redirect to progress page
                    window.location.href = `/progress.html?job_id=${result.job_id}&topic=${encodeURIComponent(result.topic)}`;
                } else {
                    throw new Error('Failed to start job');
                }
            } catch (error) {
                alert('Error starting job: ' + error.message);
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
    </script>
</body>
</html> 
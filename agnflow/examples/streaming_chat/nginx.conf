events {
    worker_connections 1024;
}

http {
    upstream chat_backend {
        # 轮询负载均衡
        server chat_server_1:8000;
        server chat_server_2:8000;
        server chat_server_3:8000;
    }

    upstream websocket_backend {
        # WebSocket连接需要保持到同一服务器
        server chat_server_1:8000;
        server chat_server_2:8000;
        server chat_server_3:8000;
    }

    server {
        listen 80;
        server_name localhost;

        # 静态文件
        location /assets/ {
            proxy_pass http://chat_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # API路由
        location /api/ {
            proxy_pass http://chat_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket连接
        location /ws {
            proxy_pass http://websocket_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket超时设置
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
        }

        # 根路径
        location / {
            proxy_pass http://chat_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
} 
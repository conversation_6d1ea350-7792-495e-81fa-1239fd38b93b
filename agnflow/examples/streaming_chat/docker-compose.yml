version: '3.8'

services:
  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: agnflow_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 聊天服务实例1
  chat_server_1:
    build: .
    container_name: agnflow_chat_1
    ports:
      - "8001:8000"
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./chat_history.sqlite3:/app/chat_history.sqlite3
    command: python server.py

  # 聊天服务实例2
  chat_server_2:
    build: .
    container_name: agnflow_chat_2
    ports:
      - "8002:8000"
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./chat_history.sqlite3:/app/chat_history.sqlite3
    command: python server.py

  # 聊天服务实例3
  chat_server_3:
    build: .
    container_name: agnflow_chat_3
    ports:
      - "8003:8000"
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./chat_history.sqlite3:/app/chat_history.sqlite3
    command: python server.py

  # Nginx负载均衡器
  nginx:
    image: nginx:alpine
    container_name: agnflow_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - chat_server_1
      - chat_server_2
      - chat_server_3

volumes:
  redis_data: 
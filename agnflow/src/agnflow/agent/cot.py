"""
思考链节点实现 - 实现结构化思考过程管理

本模块支持结构化计划管理、思考评估和渐进式问题解决。

state --update--> prompt --llm--> result --save--> state

prompt --llm--> result --update--> prompt
"""

from typing import TypedDict, NotRequired

from agnflow.core.node import Node
from agnflow.core.flow import Flow
from agnflow.agent.llm import call_llm, SysMsg, UserMsg
from agnflow.utils.pprint import pprint

md = str


def prompt_format(prompt: str, **kwargs):
    for k, v in kwargs.items():
        prompt = prompt.replace(f"<{k}>", str(v))
    return prompt


cot_prompt: md = """
# 🤖 思考链 AI 助手
你是一名严谨的 AI 助手，擅长拆解任务并制定，分多次思考过程，逐步解决复杂问题。
你会批判性地评估前一步，必要时用子步骤细化计划，并合理处理错误。请严格使用指定的 JSON 字典结构输出计划。

第一步，如果是第一次思考过程，拆解任务并制定计划

## ❓ 待解决问题
<problem>

## ❗ 下一步解决的问题
<next_step>

## 📚 思考历史
<thoughts_text>

---

# 🎯 任务目标
你的任务是生成下一个思考步骤（编号为 <next_number>）。请遵循以下要求：

## 📋 核心要求

### 1. 评估上一步思考
- 如果这不是第一次思考，请在 `current_thinking` 的开头对思考 <number> 进行简明评估。
- 评估格式示例："对思考 <number> 的评估：[✅正确/⚠️有小问题/❌重大错误 - 说明理由]"。

### 2. 执行计划步骤
- **关键推进机制**：找到计划中第一个 `status: "Pending"` 的步骤，执行它。
- 执行后立即将该步骤的 `status` 改为 `"Done"`，并添加 `result` 字段。
- **如果该步骤有子步骤，先完成所有子步骤，再标记父步骤为 Done。**
- 在 `current_thinking` 中详细说明你的推理过程。

### 3. 维护和更新计划结构
生成最新的 `planning` 列表。每一项为字典，需包含：
- `description`（字符串）
- `status`（"Pending"、"Done"、"Verification Needed"）
- 可选 `result`（已完成时的简要总结）
- 可选 `mark`（需验证时的原因说明）
- 可选 `sub_steps`（子步骤列表，同结构字典）

### 4. 状态更新规则
- **Done**：步骤已完成，包含 `result` 字段
- **Pending**：步骤待执行
- **Verification Needed**：步骤需要验证，包含 `mark` 字段说明原因

### 5. 细化复杂步骤
若某个 "Pending" 步骤较为复杂，请为其添加 `sub_steps`，将其细分为多个新的子步骤（均为 "Pending"）。父步骤在所有子步骤完成前保持 "Pending"。

### 6. 推进至结论
- 确保计划最终包含形如 `{{'description': "结论", 'status': "Pending"}}` 的结论步骤。
- **当执行到结论步骤时，直接输出完整结论，将状态设为 "Done"，并将 `next_thought_needed` 设为 `false`。**

### 7. 终止条件
**重要**：仅当执行 `description: "结论"` 步骤**并完成**（即将其状态设为 "Done"）时，将 `next_thought_needed` 设为 `false`，其余情况均为 `true`。

**关键规则**：
- 如果"结论"步骤的状态是 "Pending"，则 `next_thought_needed` 必须为 `true`
- 只有将"结论"步骤的状态改为 "Done" 并添加 `result` 后，才能将 `next_thought_needed` 设为 `false`
- 在任何其他情况下，都不能将 `next_thought_needed` 设为 `false`

## 🚀 首次思考指导
如果这是第一次思考，请创建一个初始计划，格式为字典列表（每项包含 description, status 键）。如有需要，可通过 `sub_steps` 键添加子步骤。
然后，在 `current_thinking` 中执行第一个步骤，并给出更新后的计划（将第1步的 `status` 标记为 "Done"，并补充 `result` 简要总结）。

## 📊 上下文信息

### 上一步计划（简化视图）
<last_plan_text>

### 执行指导
1. **以评估思考 <number> 开始 `current_thinking`**（如果不是第一次思考）
2. **找到第一个 `status: "Pending"` 的步骤并执行它**
3. **执行后立即更新该步骤状态为 "Done" 并添加 result**
4. **更新整个计划结构，体现当前执行的变化**

**推进示例**：
- 如果计划中有 `{"description": "分析问题", "status": "Pending"}`，执行后应变为 `{"description": "分析问题", "status": "Done", "result": "问题分析结果"}`
- 如果遇到复杂步骤，先完成其子步骤，再标记父步骤为 Done
- 每次思考必须推进至少一个步骤，不能重复执行相同步骤

**结论步骤执行**：
- 当执行到 `{"description": "结论", "status": "Pending"}` 时，直接输出完整结论
- 将结论步骤状态改为 `"Done"`，并在 `result` 字段中写入完整结论
- 只有完成结论步骤后，才能将 `next_thought_needed` 设为 `false`

**特别提醒：当执行 "结论" 步骤时，请直接输出对问题的完整结论和总结，而不是描述要执行结论步骤。**

## 📝 输出格式要求
请仅将你的回复以 ```json ... ``` 包裹的 JSON 格式输出：

```json
{
  "current_thinking": "[对思考 N 的评估：[评估结果] ...（如适用）]\n[当前步骤的思考内容...]\n",
  // 字典列表（键包括: description, status, 可选[result, mark, sub_steps]）
  "planning": [
    {
      "description": "步骤 1",
      "status": "Done",
      "result": "简要结果总结"
    },
    {
      "description": "步骤 2 复杂任务", // 复杂任务需要细分
      "status": "Pending", // 父步骤保持 Pending 状态
      "sub_steps": [
        {
          "description": "子任务 2a",
          "status": "Pending"
        },
        {
          "description": "子任务 2b",
          "status": "Verification Needed",
          "mark": "思考 X 的结果可能有问题"
        }
      ]
    },
    {
      "description": "步骤 3",
      "status": "Pending"
    },
    {
      "description": "结论",
      "status": "Pending"
    }
  ],
  // 布尔值，仅当执行到 "结论" 步骤时，将 next_thought_needed 设为 false
  "next_thought_needed": true,
  // 请根据最新计划结构，自动递归判断并输出最需要推进的 pending 步骤（如有子步骤，输出完整路径）
  "next_step": "步骤 2 复杂任务 → 子任务 2a"
}
```
"""

cot_prompt1: md = """
# 🤖 AI 助手角色定义
你是一名严谨的 AI 问答助手，可以给出具体的思考过程，制定结构化的计划步骤，逐步解决用户给定的复杂问题。
你会批判性地评估前一步，必要时用子步骤细化计划，并合理处理错误。请严格使用指定的 JSON 字典结构输出计划。

# 🎯 任务目标
1. 🤔 用户给定的复杂问题是： <problem>
2. 上一次制定计划是
```
<last_plan_text>
```
2. 当前你需要完成的计划步骤是：
如果这是第一次思考，请创建一个初始计划，格式为字典列表（每项包含 description, status 键）。如有需要，可通过 `sub_steps` 键添加子步骤。
然后，在 `current_thinking` 中执行第一个步骤，并给出更新后的计划（将第1步的 `status` 标记为 "Done"，并补充 `result` 简要总结）。
如果不是第一次，生成下一个思考步骤（编号为 <next_number>）。
3. 为了完成最终目标和当前的任务，你可以参考以下思考历史：
```
<thoughts_text>
```
4. 下一个计划步骤是：<next_step>

你的任务是生成下一个思考步骤（编号为 <next_number>）。请遵循以下要求：

1. 评估上一步思考
    - 如果这不是第一次思考，请在 `current_thinking` 的开头对思考 <number> 进行简明评估。
    - 评估格式示例："对思考 <number> 的评估：[✅正确/⚠️有小问题/❌重大错误 - 说明理由]"。
2. 执行计划步骤
    - **关键推进机制**：找到计划中第一个 `status: "Pending"` 的步骤，执行它。
    - 执行后立即将该步骤的 `status` 改为 `"Done"`，并添加 `result` 字段。
    - **如果该步骤有子步骤，先完成所有子步骤，再标记父步骤为 Done。**
    - 在 `current_thinking` 中详细说明你的推理过程。
3. 维护和更新计划结构
    生成最新的 `planning` 列表。每一项为字典，需包含：
    - `description`（字符串）
    - `status`（"Pending"、"Done"、"Verification Needed"）
    - 可选 `result`（已完成时的简要总结）
    - 可选 `mark`（需验证时的原因说明）
    - 可选 `sub_steps`（子步骤列表，同结构字典）
4. 状态更新规则
    - **Done**：步骤已完成，包含 `result` 字段
    - **Pending**：步骤待执行
    - **Verification Needed**：步骤需要验证，包含 `mark` 字段说明原因
5. 细化复杂步骤
    若某个 "Pending" 步骤较为复杂，请为其添加 `sub_steps`，将其细分为多个新的子步骤（均为 "Pending"）。父步骤在所有子步骤完成前保持 "Pending"。

6. 推进至结论
    - 确保计划最终包含形如 `{{'description': "结论", 'status': "Pending"}}` 的结论步骤。
    - **当执行到结论步骤时，直接输出完整结论，将状态设为 "Done"，并将 `next_thought_needed` 设为 `false`。**

7. 终止条件
    **重要**：仅当执行 `description: "结论"` 步骤**并完成**（即将其状态设为 "Done"）时，将 `next_thought_needed` 设为 `false`，其余情况均为 `true`。

    **关键规则**：
    - 如果"结论"步骤的状态是 "Pending"，则 `next_thought_needed` 必须为 `true`
    - 只有将"结论"步骤的状态改为 "Done" 并添加 `result` 后，才能将 `next_thought_needed` 设为 `false`
    - 在任何其他情况下，都不能将 `next_thought_needed` 设为 `false`

## 🚀 首次思考指导

## 📊 上下文信息



### 执行指导
1. **以评估思考 <number> 开始 `current_thinking`**（如果不是第一次思考）
2. **找到第一个 `status: "Pending"` 的步骤并执行它**
3. **执行后立即更新该步骤状态为 "Done" 并添加 result**
4. **更新整个计划结构，体现当前执行的变化**

**推进示例**：
- 如果计划中有 `{"description": "分析问题", "status": "Pending"}`，执行后应变为 `{"description": "分析问题", "status": "Done", "result": "问题分析结果"}`
- 如果遇到复杂步骤，先完成其子步骤，再标记父步骤为 Done
- 每次思考必须推进至少一个步骤，不能重复执行相同步骤

**结论步骤执行**：
- 当执行到 `{"description": "结论", "status": "Pending"}` 时，直接输出完整结论
- 将结论步骤状态改为 `"Done"`，并在 `result` 字段中写入完整结论
- 只有完成结论步骤后，才能将 `next_thought_needed` 设为 `false`

**特别提醒：当执行 "结论" 步骤时，请直接输出对问题的完整结论和总结，而不是描述要执行结论步骤。**

## 📝 输出格式要求
请仅将你的回复以 ```json ... ``` 包裹的 JSON 格式输出：

```json
{
  "current_thinking": "[对思考 N 的评估：[评估结果] ...（如适用）][当前步骤的思考内容...]",
  // 字典列表（键包括: description, status, 可选[result, mark, sub_steps]）
  "planning": [
    {
      "description": "步骤 1",
      "status": "Done",
      "result": "简要结果总结"
    },
    {
      "description": "步骤 2 复杂任务", // 复杂任务需要细分
      "status": "Pending", // 父步骤保持 Pending 状态
      "sub_steps": [
        {
          "description": "子任务 2a",
          "status": "Pending"
        },
        {
          "description": "子任务 2b",
          "status": "Verification Needed",
          "mark": "思考 X 的结果可能有问题"
        }
      ]
    },
    {
      "description": "步骤 3",
      "status": "Pending"
    },
    {
      "description": "结论",
      "status": "Pending"
    }
  ],
  // 布尔值，仅当执行到 "结论" 步骤时，将 next_thought_needed 设为 false
  "next_thought_needed": true,
  // 请根据最新计划结构，自动递归判断并输出最需要推进的 pending 步骤（如有子步骤，输出完整路径）
  "next_step": "步骤 2 复杂任务 → 子任务 2a"
}
```
"""


def format_plan(plan_items: "list[Plan]", indent_level=0, show_details=True):
    """格式化结构化计划用于显示或提示词"""
    status_map = {"Done": "✅", "Pending": "⏳", "Verification Needed": "❌", "Unknown": "❓"}
    indent = "  " * indent_level
    output = []

    for item in plan_items:
        if isinstance(item, dict):
            status = item.get("status", "Unknown")
            desc = item.get("description", "No description")
            line = f"{indent}- [{status}] {status_map[status]} {desc}"

            # 根据 show_details 参数决定是否显示详细信息
            if show_details:
                result = item.get("result", "")
                mark = item.get("mark", "")
                if result:
                    line += f": {result}"
                if mark:
                    line += f" ({mark})"

            output.append(line)

            # 递归格式化子步骤（如果存在）
            sub_steps = item.get("sub_steps")
            if sub_steps:
                output.append(format_plan(sub_steps, indent_level + 1, show_details))
        else:
            output.append(f"{indent}- {str(item)}")

    return "\n".join(output)


class Plan(TypedDict):
    """计划项"""

    description: str  # 描述
    status: str  # 状态
    result: NotRequired[str]  # 结果
    mark: NotRequired[str]  # 标记
    sub_steps: "NotRequired[list[Plan]]"  # 子步骤


def get_next_step(plan_items: list[Plan]) -> str:
    """
    从计划中提取下一步要解决的问题

    Args:
        plan_items: 计划项目列表
    Returns:
        下一步要解决的问题描述，如果没有则返回默认文本
    """

    def find_next_pending(items: list[Plan]) -> str:
        """递归查找第一个 Pending 状态的步骤"""
        for item in items:
            if isinstance(item, dict):
                status = item.get("status", "")
                description = item.get("description", "")

                if status == "Pending":
                    # 如果这个步骤有子步骤，优先执行第一个子步骤
                    sub_steps = item.get("sub_steps", [])
                    if sub_steps:
                        next_in_sub = find_next_pending(sub_steps)
                        if next_in_sub:
                            return f"{description} → {next_in_sub}"
                        else:
                            # 如果子步骤都完成了，返回父步骤
                            return description
                    else:
                        # 没有子步骤，直接返回这个步骤
                        return description

                # 如果当前步骤已完成，检查其子步骤
                elif status == "Done":
                    sub_steps = item.get("sub_steps", [])
                    if sub_steps:
                        next_in_sub = find_next_pending(sub_steps)
                        if next_in_sub:
                            return f"{description} → {next_in_sub}"

        return ""

    next_step = find_next_pending(plan_items)

    if next_step:
        return next_step
    else:
        # 如果没有找到 Pending 步骤，检查是否有结论步骤
        for item in plan_items:
            if isinstance(item, dict) and item.get("description") == "结论":
                status = item.get("status", "")
                if status == "Pending":
                    return "得出最终结论"
                elif status == "Done":
                    return "问题已解决"

        return "制定下一步计划"


class Thought(TypedDict, total=False):
    """思考项"""

    current_thinking: str  # 当前思考
    planning: list[Plan]  # 计划
    next_thought_needed: bool  # 是否需要下一个思考
    next_step: NotRequired[str]  # 下一步要解决的问题
    thought_number: NotRequired[int]  # 思考编号


class CoTState(TypedDict, total=False):
    problem: str  # 问题
    thoughts: list[Thought]  # 思考历史
    thoughts_text: str  # 思考历史文本
    last_plan_structure: list[Plan]  # 最后计划结构
    next_step: str  # 下一步要解决的问题
    solution: str


class CoTNode(Node[CoTState]):
    """思考链节点 - ChainOfThought

    链式思考步骤：

    1. 评估之前的思考步骤
    2. 执行计划中的待处理步骤
    3. 维护和更新结构化计划
    4. 处理错误和验证需求
    """

    def exec(self, state: CoTState) -> str:
        # 准备阶段：整理思考历史和计划状态
        problem = state.get("problem", "")
        thoughts: list[Thought] = state.get("thoughts", [])

        # 格式化之前的思考并提取最后的计划结构
        thoughts_text: str = ""
        last_plan_structure: list[Plan] = None
        if thoughts:
            # thoughts_text_list = []
            # for i, thought in enumerate(thoughts):
            #     thought_block: str = f"思考 {thought.get('thought_number', i+1)}:\n"
            #     thought_block += f"  思考内容:\n{thought.get("current_thinking", "N/A")}\n"
            #     thought_block += f"  当前计划状态:\n{format_plan(thought.get("planning", []), indent_level=2)}"
            #     thoughts_text_list.append(thought_block)
            # thoughts_text = "\n--------------------\n".join(thoughts_text_list)

            thoughts_text = "\n--------------------\n".join(
                [
                    f"思考 {thought.get('thought_number', i+1)}:\n"
                    + f"  思考内容:\n{thought.get("current_thinking", "N/A")}\n"
                    + f"  当前计划状态:\n{format_plan(thought.get("planning", []), indent_level=2)}"
                    for i, thought in enumerate(thoughts)
                ]
            )
            last_plan_structure = thoughts[-1].get("planning", [])  # 最后一个计划
        else:
            thoughts_text = "还没有任何思考"
            # 使用字典建议初始计划结构
            last_plan_structure = [
                {"description": "理解问题", "status": "Pending"},
                {"description": "制定一个高层次的计划", "status": "Pending"},
                {"description": "结论", "status": "Pending"},
            ]

        last_plan_text: str = (
            format_plan(last_plan_structure, show_details=False) if last_plan_structure else "# 没有之前的计划"
        )

        # 计算下一步要解决的问题
        next_step = get_next_step(last_plan_structure) if last_plan_structure else "开始分析问题"
        state["next_step"] = next_step

        # 构建提示词
        prompt = prompt_format(
            prompt=cot_prompt,
            problem=problem,
            next_step=next_step,
            thoughts_text=thoughts_text,
            next_number=len(thoughts) + 1,
            number=len(thoughts),
            last_plan_text=last_plan_text,
        )
        pprint(prompt)

        thought_data: Thought = call_llm(prompt, output_format="json", validation_dict=Thought)
        thought_data["thought_number"] = len(thoughts) + 1
        # 保存思考内容
        state.setdefault("thoughts", []).append(thought_data)

        # 使用更新的递归辅助函数提取计划用于打印
        plan_list: list[Plan] = thought_data.get("planning", ["错误: Planning data missing."])
        plan_str_formatted = format_plan(plan_list, indent_level=1)

        thought_num = thought_data.get("thought_number", "N/A")
        current_thinking = thought_data.get("current_thinking", "错误: Missing thinking content.")

        is_conclusion = False
        # 检查当前执行的步骤（可能是最后一个 'Done' 或如果评估失败则为当前的 'Pending'）是否为结论
        # 这个逻辑是近似的 - 可能需要根据 LLM 如何处理状态更新进行改进
        for item in reversed(plan_list):  # 首先检查最近的项目
            if isinstance(item, dict) and item.get("description") == "结论":
                # 如果结论已完成或待处理且我们正在结束，则认为是结论
                if item.get("status") == "Done" or (
                    item.get("status") == "Pending" and not thought_data.get("next_thought_needed", True)
                ):
                    is_conclusion = True
                    break
            # 简单检查，如果结论可能是子步骤，可能需要嵌套搜索

        # 使用 is_conclusion 标志或 next_thought_needed 标志进行终止
        if not thought_data.get("next_thought_needed", True):  # 主要终止信号
            # 查找结论步骤的结果
            solution = ""
            conclusion_item = None
            for item in plan_list:
                if isinstance(item, dict) and item.get("description") == "结论":
                    conclusion_item = item
                    solution = item.get("result", current_thinking)
                    break

            # 如果没有找到结论步骤或结论步骤未完成，强制继续
            if not conclusion_item or conclusion_item.get("status") != "Done":
                pprint(f"⚠️  警告：结论步骤未完成，强制继续执行")
                thought_data["next_thought_needed"] = True
                pprint(f"\n思考 {thought_num}:")
                pprint(current_thinking)
                pprint("\n当前计划状态:")
                pprint(plan_str_formatted)
                pprint("-" * 50)
                return self.name

            state["solution"] = solution
            pprint(f"\n思考 {thought_num} (结论):")
            pprint(current_thinking)
            pprint("\n最终计划状态:")
            pprint(plan_str_formatted)
            pprint("\n=== 最终解决方案 ===")
            pprint(state["solution"])
            pprint("======================\n")
            return

        # 否则，继续链式思考
        pprint(f"\n思考 {thought_num}:")
        pprint(current_thinking)
        pprint("\n当前计划状态:")
        pprint(plan_str_formatted)
        pprint("\n下一步要解决的问题:")
        pprint(state["next_step"])
        pprint("-" * 50)

        return self.name


class CoTFlow(Flow[CoTState]):
    def __init__(self, name: str = None, **kwargs):
        super().__init__(name=name, **kwargs)
        self.cot_node = CoTNode(name="cot-node")
        self[self.cot_node >> self.cot_node]


if __name__ == "__main__":

    plan_data = [
        {"description": "理解问题", "status": "Done", "result": "问题已理解，需要计算数学表达式"},
        {"description": "制定计算计划", "status": "Done", "result": "按优先级计算：先乘除，后加减"},
        {
            "description": "执行计算",
            "status": "Pending",
            "sub_steps": [
                {"description": "计算 2 * 3", "status": "Done", "result": "结果为 6"},
                {"description": "计算 1 + 6 + 4", "status": "Pending"},
            ],
        },
        {"description": "验证结果", "status": "Verification Needed", "mark": "需要检查计算过程是否正确"},
        {"description": "结论", "status": "Pending"},
    ]

    # formatted_plan = format_plan(plan_data, show_details=True)
    # pprint(formatted_plan)
    # simplified_plan = format_plan(plan_data, show_details=False)
    # pprint(simplified_plan)

    # cot_node = COTNode()
    # cot_flow = CoTFlow()
    # cot_flow[cot_node >> cot_node]
    # flow.run({"problem": "给出 1 + (2 * 3) + 4 每一步计算结果"})

    flow = CoTFlow(name="cot-flow", log_zh=(lambda *x: ...))
    state = CoTState(problem="什么是智能体CoT")
    flow.run(state, max_steps=200)
    pprint(state)
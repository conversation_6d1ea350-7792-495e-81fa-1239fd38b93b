import os
from serpapi import GoogleSearch
from typing import Dict, List, Optional


class SearchTool:
    """工具用于使用 SerpAPI 进行网络搜索"""

    def __init__(self, api_key: Optional[str] = None):
        """初始化搜索工具并设置 API 密钥"""
        self.api_key = api_key or os.getenv("SERPAPI_API_KEY")
        if not self.api_key:
            raise ValueError("SerpAPI key not found. Set SERPAPI_API_KEY env var.")

    def search(self, query: str, num_results: int = 5) -> List[Dict]:
        """使用 SerpAPI 执行 Google 搜索"""
        # 配置搜索参数
        params = {"engine": "google", "q": query, "api_key": self.api_key, "num": num_results}

        try:
            # 执行搜索
            search = GoogleSearch(params)
            results = search.get_dict()

            # 提取有机结果
            if "organic_results" not in results:
                return []

            processed_results = []
            for result in results["organic_results"][:num_results]:
                processed_results.append(
                    {
                        "title": result.get("title", ""),
                        "snippet": result.get("snippet", ""),
                        "link": result.get("link", ""),
                    }
                )

            return processed_results

        except Exception as e:
            print(f"搜索错误: {str(e)}")
            return []

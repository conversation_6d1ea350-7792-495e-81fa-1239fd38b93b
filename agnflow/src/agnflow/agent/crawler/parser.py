from typing import Dict, List
from agnflow.agent.llm import call_llm

def analyze_content(content: Dict) -> Dict:
    """使用 LLM 分析网页内容"""
    prompt = f"""
分析这个网页内容:

标题: {content['title']}
URL: {content['url']}
内容: {content['text'][:2000]}  # 限制内容长度

请提供:
1. 一段简短的摘要 (2-3 句话)
2. 主要主题/关键词 (最多 5 个)
3. 内容类型 (文章, 产品页面等)

输出 YAML 格式:
```yaml
summary: >
    摘要在这里
topics:
    - 主题 1
    - 主题 2
content_type: 类型在这里
```
"""

    try:
        response = call_llm(prompt)
        # Extract YAML between code fences
        yaml_str = response.split("```yaml")[1].split("```")[0].strip()

        import yaml

        analysis = yaml.safe_load(yaml_str)

        # Validate required fields
        assert "summary" in analysis
        assert "topics" in analysis
        assert "content_type" in analysis
        assert isinstance(analysis["topics"], list)

        return analysis

    except Exception as e:
        print(f"分析内容时出错: {str(e)}")
        return {"summary": "分析内容时出错", "topics": [], "content_type": "unknown"}


def analyze_site(crawl_results: List[Dict]) -> List[Dict]:
    """分析所有爬取的页面"""
    analyzed_results = []

    for content in crawl_results:
        if content and content.get("text"):
            analysis = analyze_content(content)
            content["analysis"] = analysis
            analyzed_results.append(content)

    return analyzed_results


def analyze_results(query: str, results: List[Dict]) -> Dict:
    """分析搜索结果使用 LLM"""
    # Format results for prompt
    formatted_results = []
    for i, result in enumerate(results, 1):
        formatted_results.append(
            f"""
结果 {i}:
标题: {result['title']}
片段: {result['snippet']}
URL: {result['link']}
"""
        )

    prompt = f"""
分析这些搜索结果对于查询: "{query}"

{'\n'.join(formatted_results)}

请提供:
1. 简要总结 (2-3 句话)
2. 关键点或事实 (最多 5 个要点)
3. 建议的后续查询 (2-3 个)

输出为 YAML 格式:
```yaml
summary: >
    brief summary here
key_points:
    - point 1
    - point 2
follow_up_queries:
    - query 1
    - query 2
```
"""

    try:
        response = call_llm(prompt)
        # Extract YAML between code fences
        yaml_str = response.split("```yaml")[1].split("```")[0].strip()

        import yaml

        analysis = yaml.safe_load(yaml_str)

        # Validate required fields
        assert "summary" in analysis
        assert "key_points" in analysis
        assert "follow_up_queries" in analysis
        assert isinstance(analysis["key_points"], list)
        assert isinstance(analysis["follow_up_queries"], list)

        return analysis

    except Exception as e:
        print(f"分析结果时出错: {str(e)}")
        return {"summary": "分析结果时出错", "key_points": [], "follow_up_queries": []}

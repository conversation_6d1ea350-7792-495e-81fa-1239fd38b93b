"""
思考链节点实现 - 实现结构化思考过程管理，支持结构化计划管理、思考评估和渐进式问题解决。

state --update--> prompt --llm--> result --save--> state
prompt --llm--> result --update--> prompt
"""

from typing import TypedDict, NotRequired

from agnflow.core.node import Node
from agnflow.core.flow import Flow
from agnflow.agent.llm import call_llm
from agnflow.utils.pprint import pprint

md = str


def prompt_format(prompt: str, **kwargs):
    for k, v in kwargs.items():
        prompt = prompt.replace(f"<{k}>", str(v))
    return prompt


cot_prompt: md = """
# 🤖 思考链 AI 助手
你是一名严谨的 AI 问答助手，可以给出具体的思考过程，制定结构化的计划步骤，逐步解决用户给定的复杂问题。
你会批判性地评估前一步，必要时用子步骤细化计划，并合理处理错误。请严格使用指定的 JSON 字典结构输出计划。

## ❓ 你最终需要解决的问题是：【<problem>】

## ❗ 当前需要解决的问题是：【<next_step>】，你需要把任务状态改为 `status: "Done"` ，然后补充 result 字段

## 📚 以下是你的思考历史
<thoughts_text>

---

# 🎯 任务目标
你的任务是生成下一个思考步骤（编号为 <next_number>）。请遵循以下要求：

## 📋 核心要求

### 1. 评估上一步思考
- 如果这不是第一次思考，请在 `thinking` 的开头对思考 <number> 进行简明评估。
- 评估格式示例："对思考 <number> 的评估：[✅正确/⚠️有小问题/❌重大错误 - 说明理由]"。

### 2. 执行计划步骤
- 执行计划中第一个状态为待执行（`status: "Pending"`）的步骤，执行后把步骤状态改为已完成（`status: "Done"`），并添加 `result` 字段。
- 如果该步骤有子步骤，先完成所有子步骤，再标记父步骤为已完成。
- 在 `thinking` 中详细说明你的推理过程。

### 3. 维护和更新计划结构（示例如下）
```json
{
  "planning": [
    {
      "description": "步骤描述",
      "status": "Done" / "Pending",  // 当前步骤状态 对应 已完成/待执行
      "result": "对description的简要总结",
      "sub_steps": [ // 子步骤列表，细化父步骤内容，结构与父步骤相同
        {
          "description": "子步骤描述",
          "status": "Done" / "Pending", // 只有所有子步骤状态为 Done，父步骤才能标记为 Done
          "result": "对description的简要总结"
        }
      ]
    }
  ]
}
```

## 🚀 首次思考指导
如果这是第一次思考，请创建一个初始计划，格式为字典列表（每项包含 description, status 键）。如有需要，可通过 `sub_steps` 键添加子步骤。
然后，在 `thinking` 中执行第一个步骤，并给出更新后的计划（将第1步的 `status` 标记为 "Done"，并补充 `result` 简要总结）。

## 📊 上下文信息

### 上一步计划（简化视图）
<last_plan_text>

### 执行指导
1. **以评估思考 <number> 开始 `thinking`**（如果不是第一次思考）
2. **找到第一个 `status: "Pending"` 的步骤并执行它**
3. **执行后立即更新该步骤状态为 "Done" 并添加 result**
4. **更新整个计划结构，体现当前执行的变化**

**推进示例**：
- 如果计划中有 `{"description": "分析问题", "status": "Pending"}`，执行后应变为 `{"description": "分析问题", "status": "Done", "result": "问题分析结果"}`
- 如果遇到复杂步骤，先完成其子步骤，再标记父步骤为 Done
- 每次思考必须推进至少一个步骤，不能重复执行相同步骤

**结论步骤执行**：
- 当执行到 `{"description": "结论", "status": "Pending"}` 时，直接输出完整结论
- 将结论步骤状态改为 `"Done"`，并在 `result` 字段中写入完整结论

**特别提醒：当执行 "结论" 步骤时，请直接输出对问题的完整结论和总结，而不是描述要执行结论步骤。**

## 📝 输出格式要求
请仅将你的回复以 ```json ... ``` 包裹的 JSON 格式输出：

```json
{
  "thinking": "[对思考 N 的评估：[评估结果] ...（如适用）]\n[当前步骤的思考内容...]\n",
  // 字典列表（键包括: description, status, 可选[result, sub_steps]）
  "planning": [
    {
      "description": "步骤 1",
      "status": "Done",
      "result": "简要结果总结"
    },
    {
      "description": "步骤 2 复杂任务", // 复杂任务需要细分
      "status": "Pending", // 父步骤保持 Pending 状态
      "sub_steps": [
        {
          "description": "子任务 2a",
          "status": "Pending"
        },
        {
          "description": "子任务 2b",
          "status": "Pending",
        }
      ]
    },
    {
      "description": "步骤 3",
      "status": "Pending"
    },
    {
      "description": "结论",
      "status": "Pending"
    }
  ],
  // 请根据最新计划结构，自动递归判断并输出最需要推进的 pending 步骤（如有子步骤，输出完整路径）
  "next_step": "步骤 2 复杂任务 → 子任务 2a"
}
```
"""


def plan_to_text(plan_items: "list[Plan]", indent_level=0, show_details=True):
    """格式化结构化计划用于显示或提示词"""
    status_map = {"Done": "✅", "Pending": "⏳", "Unknown": "❓"}
    indent = "  " * indent_level
    output = []

    for item in plan_items:
        if isinstance(item, dict):
            status = item.get("status", "未知")
            desc = item.get("description", "无")
            line = f"{indent}- [{status}] {status_map[status]} {desc}"

            # 根据 show_details 参数决定是否显示详细信息
            if show_details:
                result = item.get("result", "")
                if result:
                    line += f": {result}"
            output.append(line)

            # 递归格式化子步骤（如果存在）
            sub_steps = item.get("sub_steps")
            if sub_steps:
                output.append(plan_to_text(sub_steps, indent_level + 1, show_details))
        else:
            output.append(f"{indent}- {str(item)}")

    return "\n".join(output)


def thought_to_text(thoughts: "list[Thought]") -> str:
    """格式化思考用于显示或提示词"""
    return (
        "\n--------------------\n".join(
            [
                f"思考 {i+1}:\n"
                + f"  思考内容:\n{thought.get("thinking", "N/A")}\n"
                + f"  当前计划状态:\n{plan_to_text(thought.get("planning", []), indent_level=2)}"
                for i, thought in enumerate(thoughts)
            ]
        )
        if thoughts
        else "还没有任何思考"
    )


def get_next_step(plan_items: "list[Plan]") -> str:
    """从计划中提取下一步要解决的问题"""

    def find_next_pending(items: list[Plan]) -> str:
        """递归查找第一个 Pending 状态的步骤"""
        for item in items:
            if isinstance(item, dict):
                status = item.get("status", "")
                description = item.get("description", "")

                if status == "Pending":
                    # 如果这个步骤有子步骤，优先执行第一个子步骤
                    sub_steps = item.get("sub_steps", [])
                    if sub_steps:
                        next_in_sub = find_next_pending(sub_steps)
                        if next_in_sub:
                            return f"{description} → {next_in_sub}"
                        else:
                            # 如果子步骤都完成了，返回父步骤
                            return description
                    else:
                        # 没有子步骤，直接返回这个步骤
                        return description

                # 如果当前步骤已完成，检查其子步骤
                elif status == "Done":
                    sub_steps = item.get("sub_steps", [])
                    if sub_steps:
                        next_in_sub = find_next_pending(sub_steps)
                        if next_in_sub:
                            return f"{description} → {next_in_sub}"

        return ""

    next_step = find_next_pending(plan_items)

    if next_step:
        return next_step
    else:
        # 如果没有找到 Pending 步骤，检查是否有结论步骤
        for item in plan_items:
            if isinstance(item, dict) and item.get("description") == "结论":
                status = item.get("status", "")
                if status == "Pending":
                    return "得出最终结论"
                elif status == "Done":
                    return "问题已解决"

        return "制定下一步计划"


class Plan(TypedDict):
    """计划项"""

    description: str  # 描述
    status: str  # 状态
    result: NotRequired[str]  # 结果
    sub_steps: "NotRequired[list[Plan]]"  # 子步骤


class Thought(TypedDict, total=False):
    """思考项"""

    thinking: str  # 当前思考
    planning: list[Plan]  # 计划
    next_step: NotRequired[str]  # 下一步要解决的问题


class CoTState(TypedDict, total=False):
    problem: str  # 问题
    thoughts: list[Thought]  # 思考历史
    solution: str


class CoTNode(Node[CoTState]):
    """思考链节点 - ChainOfThought"""

    def exec(self, state: CoTState) -> str:
        # 准备阶段：整理思考历史和计划状态
        problem = state.get("problem", "")
        thoughts: list[Thought] = state.get("thoughts", [])

        # 思考历史
        thoughts_text: str = thought_to_text(thoughts)

        # 最后一个计划
        last_plan: list[Plan] = (
            thoughts[-1].get("planning", [])
            if thoughts
            else [
                {"description": "理解问题", "status": "Pending"},
                {"description": "制定一个高层次的计划", "status": "Pending"},
                {"description": "结论", "status": "Pending"},
            ]
        )
        last_plan_text: str = plan_to_text(last_plan, show_details=False) if last_plan else "# 没有之前的计划"

        # 计算下一步要解决的问题
        next_step = get_next_step(last_plan) if last_plan else "开始分析问题"

        # 构建提示词
        prompt = prompt_format(
            prompt=cot_prompt,
            problem=problem,
            next_step=next_step,
            thoughts_text=thoughts_text,
            next_number=len(thoughts) + 1,
            number=len(thoughts),
            last_plan_text=last_plan_text,
        )
        # pprint(prompt)

        thought_data: Thought = call_llm(prompt, output_format="json", validation_dict=Thought)
        # 保存思考内容
        state.setdefault("thoughts", []).append(thought_data)

        # 使用更新的递归辅助函数提取计划用于打印
        plan_list: list[Plan] = thought_data.get("planning", [{"status": "Pending", "description": "错误: Planning 数据缺失"}])
        plan_text = plan_to_text(plan_list, indent_level=1)

        thinking = thought_data.get("thinking")

        def has_pending_step(plan_list: list[Plan]) -> bool:
            for item in plan_list:
                if not isinstance(item, dict) or item.get("status") == "Pending":
                    return True
                for sub_item in item.get("sub_steps", []):
                    return has_pending_step(sub_item)
            return False

        if has_pending_step(plan_list):
            pprint(f"思考 {len(thoughts) + 1}:\n{thinking}\n")
            pprint(f"当前计划状态:\n{plan_text}\n")
            pprint(f"下一步要解决的问题:\n{next_step}\n")
            pprint("-" * 50)
            return self.name
        else:
            state["solution"] = plan_list[-1].get("result", "")
            pprint(f"思考 {len(thoughts) + 1} (结论):\n{thinking}\n")
            pprint(f"最终计划状态:\n{plan_text}\n")
            pprint(f"=== 最终解决方案 ===\n{state['solution']}")
            return


class CoTFlow(Flow[CoTState]):
    def __init__(self, name: str = None, **kwargs):
        super().__init__(name=name, **kwargs)
        self.cot_node = CoTNode(name="cot-node")
        self[self.cot_node >> self.cot_node]


if __name__ == "__main__":

    plan_data = [
        {"description": "理解问题", "status": "Done", "result": "问题已理解，需要计算数学表达式"},
        {"description": "制定计算计划", "status": "Done", "result": "按优先级计算：先乘除，后加减"},
        {
            "description": "执行计算",
            "status": "Pending",
            "sub_steps": [
                {"description": "计算 2 * 3", "status": "Done", "result": "结果为 6"},
                {"description": "计算 1 + 6 + 4", "status": "Pending"},
            ],
        },
        {"description": "验证结果", "status": "Pending"},
        {"description": "结论", "status": "Pending"},
    ]

    # formatted_plan = format_plan(plan_data, show_details=True)
    # pprint(formatted_plan)
    # simplified_plan = format_plan(plan_data, show_details=False)
    # pprint(simplified_plan)

    # cot_node = COTNode()
    # cot_flow = CoTFlow()
    # cot_flow[cot_node >> cot_node]
    # flow.run({"problem": "给出 1 + (2 * 3) + 4 每一步计算结果"})

    flow = CoTFlow(name="cot-flow", log_zh=(lambda *x: ...))
    state = CoTState(problem="什么是智能体CoT")
    flow.run(state, max_steps=200)
    pprint(state)

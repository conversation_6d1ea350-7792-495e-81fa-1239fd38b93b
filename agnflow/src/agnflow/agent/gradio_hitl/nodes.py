from datetime import datetime
from queue import Queue
from textwrap import dedent

import yaml
from agnflow.core import Node

from agnflow.agent.llm import call_llm
from .utils.call_mock_api import call_book_hotel_api, call_check_weather_api
from .utils.conversation import load_conversation, save_conversation
from .utils.format_chat_history import format_chat_history


class DecideAction(Node):
    def prep(self, state):
        conversation_id = state["conversation_id"]
        session = load_conversation(conversation_id)
        return session, state["history"], state["query"]

    def exec(self, prep_res):
        session, history, query = prep_res
        prompt = dedent(
            f"""
            ### 指令
            你是一名生活助理，可以帮助用户预订酒店和查询天气。
            你需要根据你的上一个动作、动作执行结果、聊天历史和当前用户问题来决定下一步动作。

            ### 聊天历史
            {format_chat_history(history)}

            ### 当前用户问题
            user: {query}

            ### 上下文
            上一个动作: {session.get("last_action", None)}
            上一个动作结果: {session.get("action_result", None)}
            当前日期: {datetime.now().date()} 

            ### 动作空间
            [1] check-weather
            描述：当用户询问天气时，使用此工具。
            参数：
                - name: city
                    描述：要查询天气的城市
                    必填：是
                    示例：北京
                - name: date
                    描述：要查询天气的日期，如未提供则使用当前日期
                    必填：否
                    示例：2025-05-28

            [2] book-hotel
            描述：当用户想要预订酒店时，使用此工具。
            参数：
                - name: hotel
                    描述：要预订的酒店名称
                    必填：是
                    示例：上海希尔顿酒店
                - name: checkin_date
                    描述：入住日期
                    必填：是
                    示例：2025-05-28
                - name: checkout_date
                    描述：退房日期
                    必填：是
                    示例：2025-05-29

            [3] follow-up
            描述：1. 当用户的问题超出预订酒店和查询天气的范围时，使用此工具引导用户；2. 当当前信息无法满足相应工具的参数要求时，使用此工具向用户追问。
            参数：
                - name: question
                    描述：你对用户的引导或追问，保持热情活泼的语言风格，并使用与用户问题相同的语言。
                    必填：是
                    示例：您想预订哪家酒店呢？😊

            [4] result-notification
            描述：当酒店预订或天气查询完成后，使用此工具通知用户结果，并询问是否还需要其他帮助。如果你发现用户的问题在历史对话中未完成，可以在最后一步引导用户完成意图。
            参数：
                - name: result
                    描述：根据上一个动作结果通知用户。保持热情活泼的语言风格，并使用与用户问题相同的语言。
                    必填：是
                    示例：酒店已为您成功预订。😉\n\n入住日期为XX，退房日期为XX。感谢您的使用，还需要其他帮助吗？😀

            ## 下一步动作
            请根据上下文和可用动作决定下一步。
            返回格式如下：

            ```yaml
            thinking: |
                <你的逐步推理过程>
            action: check-weather OR book-hotel OR follow-up OR result-notification
            reason: <你选择该动作的原因>
            question: <如果动作为follow-up>
            city: <如果动作为check-weather> 
            hotel: <如果动作为book-hotel>
            checkin_date: <如果动作为book-hotel>
            checkout_date: <如果动作为book-hotel>
            result: <如果动作为result-notification>
            ```

            重要提示：请确保：
            1. 所有多行字段均使用4个空格缩进
            2. 多行文本字段使用 | 字符
            3. 单行字段不使用 | 字符
            """
        )

        response = call_llm(prompt.strip())
        yaml_str = response.split("```yaml")[1].split("````")[0].strip()
        print(f"🤖 智能体回复: \n{yaml_str}")
        decision = yaml.safe_load(yaml_str)
        return decision

    def post(self, state, prep_res, exec_res):
        conversation_id = state["conversation_id"]
        session: dict = load_conversation(conversation_id)
        """保存决策并确定流程下一步。"""
        # 如果LLM决定要查询，则保存查询参数
        session["last_action"] = exec_res["action"]
        flow_log: Queue = state["flow_queue"]

        for line in exec_res["thinking"].split("\n"):
            line = line.replace("-", "").strip()
            if line:
                flow_log.put(f"🤔 {line}")

        if exec_res["action"] == "check-weather":
            session["check_weather_params"] = {
                "city": exec_res["city"],
                "date": exec_res.get("date", None),
            }
            flow_log.put(f"➡️ 智能体决定查询天气: {exec_res['city']}")
        elif exec_res["action"] == "book-hotel":
            session["book_hotel_params"] = {
                "hotel": exec_res["hotel"],
                "checkin_date": exec_res["checkin_date"],
                "checkout_date": exec_res["checkout_date"],
            }
            flow_log.put(f"➡️ 智能体决定预订酒店: {exec_res['hotel']}")
        elif exec_res["action"] == "follow-up":
            session["follow_up_params"] = {"question": exec_res["question"]}
            flow_log.put(f"➡️ 智能体决定追问: {exec_res['question']}")
        elif exec_res["action"] == "result-notification":
            session["result_notification_params"] = {"result": exec_res["result"]}
            flow_log.put(f"➡️ 智能体决定通知结果: {exec_res['result']}")
        save_conversation(conversation_id, session)
        # 返回动作以确定流程的下一个节点
        return exec_res["action"]


class CheckWeather(Node):
    def prep(self, state):
        conversation_id = state["conversation_id"]
        session: dict = load_conversation(conversation_id)
        city = session["check_weather_params"]["city"]
        date = session["check_weather_params"].get("date", None)
        return city, date

    def exec(self, prep_res):
        city, date = prep_res
        return call_check_weather_api(city, date)

    def post(self, state, prep_res, exec_res):
        flow_log: Queue = state["flow_queue"]
        flow_log.put(f"⬅️ 天气查询结果: {exec_res}")

        conversation_id = state["conversation_id"]
        session: dict = load_conversation(conversation_id)
        session["action_result"] = exec_res
        save_conversation(conversation_id, session)
        return "default"


class BookHotel(Node):
    def prep(self, state):
        conversation_id = state["conversation_id"]
        session: dict = load_conversation(conversation_id)

        hotel = session["book_hotel_params"]["hotel"]
        checkin_date = session["book_hotel_params"]["checkin_date"]
        checkout_date = session["book_hotel_params"]["checkout_date"]
        return hotel, checkin_date, checkout_date

    def exec(self, prep_res):
        hotel, checkin_date, checkout_date = prep_res
        return call_book_hotel_api(hotel, checkin_date, checkout_date)

    def post(self, state, prep_res, exec_res):
        flow_log: Queue = state["flow_queue"]
        flow_log.put(f"⬅️ 酒店预订结果: {exec_res}")

        conversation_id = state["conversation_id"]
        session: dict = load_conversation(conversation_id)
        session["action_result"] = exec_res
        save_conversation(conversation_id, session)
        return "default"


class FollowUp(Node):
    def prep(self, state):
        flow_log: Queue = state["flow_queue"]
        flow_log.put(None)

        conversation_id = state["conversation_id"]
        session: dict = load_conversation(conversation_id)
        question = session["follow_up_params"]["question"]
        return question, state["queue"]

    def exec(self, prep_res):
        question, queue = prep_res
        queue.put(question)
        queue.put(None)
        return question

    def post(self, state, prep_res, exec_res):
        conversation_id = state["conversation_id"]
        session: dict = load_conversation(conversation_id)
        session["action_result"] = exec_res
        return "done"


class ResultNotification(Node):
    def prep(self, state):
        flow_log: Queue = state["flow_queue"]
        flow_log.put(None)

        conversation_id = state["conversation_id"]
        session: dict = load_conversation(conversation_id)
        result = session["result_notification_params"]["result"]
        return result, state["queue"]

    def exec(self, prep_res):
        result, queue = prep_res
        queue.put(result)
        queue.put(None)
        return result

    def post(self, state, prep_res, exec_res):
        conversation_id = state["conversation_id"]
        session: dict = load_conversation(conversation_id)
        session["action_result"] = None
        session["last_action"] = None
        save_conversation(conversation_id, session)
        return "done"

"""
GOAP（Goal-Oriented Action Planning，目标导向行动规划）是一种让智能体（如游戏 NPC、AI 系统）根据目标自主规划行动序列的算法。核心逻辑是：智能体先明确最终目标，再从当前状态出发，反向推导达成目标所需的一系列可行行动，最终生成一条 “当前状态→行动 1→行动 2→…→目标状态” 的路径。

TAO:
适合环境动态变化、需实时响应的场景
基于循环迭代，动态生成和调整行动
TAO 是一种执行架构，强调 “通过循环交互逐步逼近目标”。

while not goal_achieved:
    thought = llm.generate_thought(current_state, goal)  # 思考下一步
    action = llm.select_action(thought)  # 选择行动
    observation = execute_action(action)  # 执行并观察结果
    current_state = update_state(current_state, observation)  # 更新状态

GOAP （执果寻因）
适合环境确定性高、目标明确的场景
基于搜索算法（如 A*）提前规划完整行动序列
GOAP 是一种规划算法，强调 “一次性规划全局最优路径”。

"""

import openai


# 初始化 LLM（需替换为实际 API 密钥）
openai.api_key = "your-api-key"


class Action:
    def __init__(self, name, preconditions, effects, cost=1):
        self.name = name  # 行动名称
        self.preconditions = preconditions  # 前置条件（字典：状态键值对）
        self.effects = effects  # 后置效果（字典：状态键值对）
        self.cost = cost  # 行动成本

    def is_possible(self, current_state):
        """检查当前状态是否满足前置条件"""
        for key, value in self.preconditions.items():
            if current_state.get(key) != value:
                return False
        return True

    def apply(self, current_state):
        """执行行动，更新状态"""
        new_state = current_state.copy()
        new_state.update(self.effects)
        return new_state


class GOAPPlanner:
    def __init__(self, actions):
        self.actions: list[Action] = actions  # 可用行动列表

    def plan(self, current_state, goal):
        """从当前状态规划达成目标的行动序列（简化版：广度优先搜索）"""
        # 检查当前状态是否已满足目标
        if all(current_state.get(k) == v for k, v in goal.items()):
            return []

        # 广度优先搜索寻找行动序列
        from collections import deque

        queue = deque()
        queue.append((current_state, []))  # (当前状态, 已执行行动列表)
        visited = set()

        while queue:
            state, path = queue.popleft()
            state_tuple = tuple(sorted(state.items()))  # 状态哈希化，避免重复处理
            if state_tuple in visited:
                continue
            visited.add(state_tuple)

            # 尝试所有可能的行动
            for action in self.actions:
                if action.is_possible(state):
                    new_state = action.apply(state)
                    new_path = path + [action.name]
                    # 检查新状态是否满足目标
                    if all(new_state.get(k) == v for k, v in goal.items()):
                        return new_path
                    # 否则继续搜索
                    queue.append((new_state, new_path))

        return None  # 无可行路径


def llm_parse_goal(natural_goal):
    """用 LLM 将自然语言目标转为 GOAP 目标状态"""
    prompt = f"""将目标 '{natural_goal}' 转换为键值对形式的状态（True/False），例如：
    输入："我想喝到热水"
    输出：{{"有热水": True}}
    请直接返回字典，不要多余内容。"""
    response = openai.ChatCompletion.create(model="gpt-3.5-turbo", messages=[{"role": "user", "content": prompt}])
    return eval(response.choices[0].message.content)  # 简化处理，实际需加异常判断


def llm_generate_action(natural_action):
    """用 LLM 生成行动的前置条件和效果"""
    prompt = f"""分析行动 '{natural_action}'，输出前置条件（preconditions）和效果（effects），格式为：
    preconditions: {{...}}, effects: {{...}}
    例如：
    输入："炒菜"
    输出：preconditions: {{"有食材": True, "有锅": True}}, effects: {{"有熟食": True}}"""
    response = openai.ChatCompletion.create(model="gpt-3.5-turbo", messages=[{"role": "user", "content": prompt}])
    # 解析 LLM 输出为 Action 所需的字典（实际需更健壮的解析逻辑）
    result = response.choices[0].message.content
    preconditions = eval(result.split("preconditions: ")[1].split(", effects:")[0])
    effects = eval(result.split("effects: ")[1])
    return preconditions, effects


# 示例：制作面包的行动和目标
if __name__ == "__main__":
    # 定义行动
    actions = [
        Action(name="买面粉", preconditions={"有钱": True}, effects={"有面粉": True, "有钱": False}),
        Action(name="揉面", preconditions={"有面粉": True, "有水": True}, effects={"有面团": True}),
        Action(name="烤面包", preconditions={"有面团": True, "烤箱可用": True}, effects={"有面包": True}),
    ]

    # 初始化规划器
    planner = GOAPPlanner(actions)

    # 当前状态：有钱、有水、烤箱可用
    current_state = {"有钱": True, "有水": True, "烤箱可用": True}
    # 目标：有面包
    goal = {"有面包": True}

    # 规划行动序列
    plan = planner.plan(current_state, goal)
    print("行动序列:", plan)  # 输出：['买面粉', '揉面', '烤面包']

    # # 1. 用户输入自然语言目标
    # natural_goal = "我想做面包"
    # goal_state = llm_parse_goal(natural_goal)  # 输出示例：{"有面包": True}

    # # 2. 用 LLM 生成相关行动（如“买面粉”“烤面包”）
    # actions = []
    # natural_actions = ["买面粉", "揉面", "烤面包"]
    # for action_name in natural_actions:
    #     pre, eff = llm_generate_action(action_name)
    #     actions.append(Action(name=action_name, preconditions=pre, effects=eff, cost=1))

    # # 3. 调用 GOAP 规划（使用前文的 GOAPPlanner 类）
    # current_state = {"有钱": True, "有烤箱": True}
    # planner = GOAPPlanner(actions)
    # plan = planner.plan(current_state, goal_state)
    # print("行动序列:", plan)

import inspect
from typing import Annotated as A, Any, Literal, List, Dict, TypedDict, Iterable, Callable, overload
import os
import json
from openai.types.chat.chat_completion_chunk import ChatCompletionChunk
import yaml
from dotenv import load_dotenv
from openai import OpenAI, Stream
from langchain_qwq import ChatQwQ
from agnflow.utils.type import check_type
from xmltodict import parse

load_dotenv()


class Msg(List[Dict[Literal["user", "system", "assistant"], str]]):
    """消息列表类，支持链式操作
    UserMsg("你好") == [{"role": "user", "content": "你好"}]
    SysMsg("你是谁") == [{"role": "system", "content": "你是谁"}]
    AiMsg("我是AI") == [{"role": "assistant", "content": "我是AI"}]
    """

    role = None

    def __init_subclass__(cls, role: str) -> None:
        cls.role = role
    @overload
    def __init__(self, content: str): ...
    def __init__(self, msg: "Msg"): 
        if self.role and isinstance(msg, str):
            msg = [{"role": self.role, "content": msg}]
        super().__init__(msg)

    def __add__(self, other: "Msg") -> "Msg":
        """重载加法运算符，支持消息列表的合并"""
        if isinstance(other, (Msg, list)):
            return Msg(list(self) + list(other))
        else:
            raise TypeError(f"不支持的类型: {type(other)}")


class UserMsg(Msg, role="user"):
    """用户消息类，支持链式操作"""


class SysMsg(Msg, role="system"):
    """系统消息类，支持链式操作"""


class AiMsg(Msg, role="assistant"):
    """助手消息类，支持链式操作"""


def prompt_format(prompt: str, **kwargs):
    for k, v in kwargs.items():
        prompt = prompt.replace(f"<{k}>", str(v))
    return prompt


"""
可用工具

**注解说明：from typing import Annotated as A**
- 工具：add(a: A[int, "加数"], b: A[int, "加数"])，描述：加法
- 工具：sub(a: A[int, "减数"], b: A[int, "减数"])，描述：减法
- 工具：mul(a: A[int, "乘数"], b: A[int, "乘数"])，描述：乘法

输出格式
```xml
<tool>
    <name>add</name>
    <args>
        <a>1</a>
        <b>2</b>
    </args>
</tool>
```
"""


class ToolMsg(Msg, role="system"):
    def __init__(self, *tool_funcs):
        prompt = "**注解说明：from typing import Annotated as A**\n"
        for tool_func in tool_funcs:
            name = tool_func.__name__
            desc = tool_func.__doc__
            sig = inspect.signature(tool_func)
            prompt += f"- 工具：{name}{sig}，描述：{desc}\n".replace("Annotated", "A")
        super().__init__(prompt)


def add(a: A[int, "加数"], b: A[int, "加数"]) -> int:
    """加法"""
    return a + b


def sub(a: A[int, "减数"], b: A[int, "减数"]) -> int:
    """减法"""
    return a - b


def mul(a: A[int, "乘数"], b: A[int, "乘数"]) -> int:
    """乘法"""
    return a * b


tool_map = {
    "add": add,
    "sub": sub,
    "mul": mul,
}


def get_tool_prompt(*tool_funcs: Callable):
    prompt = "**注解说明：from typing import Annotated as A**\n\n"
    for tool_func in tool_funcs:
        name = tool_func.__name__
        desc = tool_func.__doc__
        sig = inspect.signature(tool_func)
        prompt += f"- 工具：{name}{sig}，描述：{desc}\n".replace("Annotated", "A")
    return prompt


# print(get_tool_prompt(add, sub, mul))

[{"name": "add", "args": {"a": 1, "b": 2}}, {"name": "sub", "args": {"a": 1, "b": 2}}]
tools = parse(
    """
<tool><name>add</name><args><a>1</a><b>2</b></args></tool>
"""
)
# print(tools["tool"])


def call_llm(
    messages: str | Msg,
    model="glm-4-flashx-250414",
    output_format: Literal["yaml", "json", "text"] = "text",
    validation_dict: dict = None,
    retry: int = 3,
):
    if isinstance(messages, str):
        messages = UserMsg(messages)

    # 初始化OpenAI客户端
    model = model or os.getenv("OPENAI_MODEL")
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    completion = client.chat.completions.create(model=model, messages=messages)
    res = completion.choices[0].message.content
    if output_format == "text":
        return res
    # 根据输出格式返回结果
    for i in range(retry):
        try:
            res = res.strip().removeprefix(f"```{output_format}").removesuffix("```").strip()
            if output_format == "yaml":
                res_obj: dict = yaml.safe_load(res)
            elif output_format == "json":
                res_obj: dict = json.loads(res)
            if validation_dict:
                for arg, type_ in validation_dict.__annotations__.items():
                    if not check_type(res_obj.get(arg), type_):
                        raise ValueError(f"验证失败: {arg} 类型不符合： {type_} 剩余重试机会: {retry - i}")
                return res_obj
            return
        except Exception as e:
            err = f"输出格式化失败: \n输出: \n{res}\n\n错误: \n{e}\n\n剩余重试机会: {retry - i}\n\n"
            print(err)
            if i == retry - 1:
                raise ValueError(err)


async def stream_llm(messages: str | Msg, model="glm-4-flashx-250414"):
    if isinstance(messages, str):
        messages = UserMsg(messages)

    model = model or os.getenv("OPENAI_MODEL")
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    chunks: Stream[ChatCompletionChunk] = client.chat.completions.create(
        model=model, messages=messages, temperature=0.7, stream=True
    )
    for chunk in chunks:
        if content := chunk.choices[0].delta.content:
            yield content


class JsonSchema(TypedDict):
    """可以通过doc或注解了解字段用途"""

    data: A[str, "数据"]
    """数据"""


def llm_json_stream(
    messages: list[Msg],
    json_schema: dict,
    mdoel: Any = None,
    validation_func=None,
):
    """
    双阶段流式输出：
    1. 第一阶段收集json（推理原因+合法性校验结果），仅用于校验。
    2. 校验通过后，第二阶段才流式输出实际内容。
    支持OpenAI、ChatQwQ等模型。
    """
    split_token = "~~~~~~"

    if mdoel is None:
        raise ValueError("必须指定mdoel参数（OpenAI/ChatQwQ等）")
    if validation_func is None:

        def validation_func(x):
            return True

    # 组装消息格式
    if isinstance(mdoel, OpenAI):
        prompt = f"请先以JSON格式输出如下schema的内容：{json.dumps(json_schema, ensure_ascii=False)}，然后输出正文内容。两部分之间用'{split_token}'分割。"
        messages[-1]["content"] = prompt + "\n" + messages[-1]["content"]
        response = mdoel.chat.completions.create(model="glm-4-flashx-250414", messages=messages, stream=True)
        buffer = ""
        json_block = ""
        in_json = True
        for chunk in response:
            delta = chunk.choices[0].delta.content or ""
            buffer += delta
            if in_json:
                json_block += delta
                if split_token in json_block:
                    json_str, rest = json_block.split(split_token, 1)
                    try:
                        json_obj = json.loads(json_str.strip())
                        if not validation_func(json_obj):
                            yield {"type": "error", "data": "JSON校验未通过"}
                            return
                        # 校验通过，开始流式输出正文
                        in_json = False
                        buffer = rest
                        break  # 跳出for，进入正文流式输出
                    except Exception as e:
                        yield {"type": "error", "data": f"JSON解析失败: {e}"}
                        return
        # 正文流式输出
        if buffer:
            yield {"type": "text", "data": buffer}
        for chunk in response:
            delta = chunk.choices[0].delta.content or ""
            if delta:
                yield {"type": "text", "data": delta}
        return
    elif isinstance(mdoel, ChatQwQ):
        prompt = f"请先以JSON格式输出如下schema的内容：{json.dumps(json_schema, ensure_ascii=False)}，然后输出正文内容。两部分之间用'---'分割。"
        full_prompt = prompt + "\n" + messages[-1]["content"]
        stream = mdoel.stream(full_prompt)
        buffer = ""
        json_block = ""
        in_json = True
        stream_iter = iter(stream)
        while True:
            try:
                delta = next(stream_iter)
            except StopIteration:
                break
            buffer += delta
            if in_json:
                json_block += delta
                if split_token in json_block:
                    json_str, rest = json_block.split(split_token, 1)
                    try:
                        json_obj = json.loads(json_str.strip())
                        if not validation_func(json_obj):
                            yield {"type": "error", "data": "JSON校验未通过"}
                            return
                        in_json = False
                        buffer = rest
                        break
                    except Exception as e:
                        yield {"type": "error", "data": f"JSON解析失败: {e}"}
                        return
        # 正文流式输出
        if buffer:
            yield {"type": "text", "data": buffer}
        for delta in stream_iter:
            if delta:
                yield {"type": "text", "data": delta}
        return
    else:
        raise NotImplementedError(f"暂不支持的模型类型: {type(mdoel)}")

    # if __name__ == "__main__":
    #     msgs = (
    #         Msg([{"role": "user", "content": "你好"}])
    #         + Msg([{"role": "system", "content": "你是谁"}])
    #         + Msg([{"role": "assistant", "content": "我是AI"}])
    #     )
    #     # msgs += UserMsg("你好2") + SysMsg("你是谁2") + AiMsg("我是AI2")
    #     # print("消息列表:", *msgs)

    #     # from openai import OpenAI

    #     # def my_validation_func(js):
    #     #     return isinstance(js, dict) and js.get("valid") is True

    #     # client = OpenAI()
    #     # json_schema = {"reason": "str", "valid": "bool"}
    #     # message = "请帮我写一个Python冒泡排序"
    #     # print("OpenAI流式输出示例：")
    #     # for chunk in llm_json_stream(
    #     #     message=message, json_schema=json_schema, history_msgs=[], mdoel=client, validation_func=my_validation_func
    #     # ):
    #     #     if chunk["type"] == "json":
    #     #         print("校验信息：", chunk["data"])
    #     #     elif chunk["type"] == "text":
    #     #         print("正文流：", chunk["data"], end="", flush=True)
    #     #     elif chunk["type"] == "error":
    #     #         print("错误：", chunk["data"])

    #     # # ChatQwQ 示例
    #     # from langchain_qwq import ChatQwQ

    #     # client2 = ChatQwQ(api_key="你的API_KEY")
    #     # print("\nChatQwQ流式输出示例：")
    #     # for chunk in llm_json_stream(
    #     #     message=message, json_schema=json_schema, history_msgs=[], mdoel=client2, validation_func=my_validation_func
    #     # ):
    #     #     if chunk["type"] == "json":
    #     #         print("校验信息：", chunk["data"])
    #     #     elif chunk["type"] == "text":
    #     #         print("正文流：", chunk["data"], end="", flush=True)
    #     #     elif chunk["type"] == "error":
    #     #         print("错误：", chunk["data"])
    #     class A(TypedDict):
    #         a: int
    #         b: str

    #     md = str
    msg: md = """
请以JSON格式输出如下内容：
```json
{
    "a": <中国人数，int>,
    "b": <中国面积，str>
}
```

"""


#     # res = call_llm(msg, output_format="json", validation_dict=A)
#     # print(res)

#     import asyncio

#     async def test():
#         messages = [{"role": "user", "content": "什么是智能体"}]
#         async for chunk in stream_llm(messages=messages, reasoning=True):
#             print(chunk, end="", flush=True)
#         print()

#     asyncio.run(test())

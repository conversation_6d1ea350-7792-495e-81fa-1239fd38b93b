"""Agent-to-Agent 节点实现。

本模块提供了智能代理决策节点，包括动作决策、网络搜索和问题回答功能。
演示了如何构建能够自主决定搜索或回答的智能代理系统。
"""

from agnflow.core import Node
from agnflow.utils.search import search_web_duckduckgo
from agnflow.agent.llm import UserMsg, call_llm
import yaml

md = str

class DecideAction(Node):
    """动作决策节点 - 决定是搜索还是回答问题"""
    def exec(self, state):
        context = state.get("context", "无先前搜索")
        question = state["question"]
        print(f"🤔 决定下一步做什么...")

        # 创建提示词帮助 LLM 决定下一步动作，使用正确的 yaml 格式
        prompt: md = f"""
### 上下文
你是一名可以进行网络搜索的研究助理。
问题: {question}
先前研究: {context}

### 可用动作
[1] search
  描述: 在网络上查找更多信息
  参数:
    - query (str): 要搜索的内容

[2] answer
  描述: 用当前知识回答问题
  参数:
    - answer (str): 问题的最终答案

## 下一步动作
请根据上下文和可用的动作，决定下一步要做什么。
请按照以下格式返回你的回复：
"""
        # 调用 LLM 做出决策
        response = call_llm(prompt)

        # 解析响应获取决策
        yaml_str = response.split("```yaml")[1].split("```")[0].strip()
        decision = yaml.safe_load(yaml_str)

        # 如果 LLM 决定搜索，保存搜索查询
        if decision["action"] == "search":
            state["search_query"] = decision["search_query"]
            print(f"🔍 决定搜索: {decision['search_query']}")
        else:
            state["context"] = decision["answer"] # 如果 LLM 不搜索就给出答案，保存上下文
            print(f"💡 决定回答问题")

        # 返回动作以确定流程中的下一个节点
        return decision["action"]

class SearchWeb(Node):
    """网络搜索节点 - 执行网络搜索"""
    def exec(self, state):
        search_query = state["search_query"]
        
        # 调用搜索工具函数
        print(f"🌐 搜索网络: {search_query}")
        results = search_web_duckduckgo(search_query)

        # 将搜索结果添加到共享存储的上下文中
        previous = state.get("context", "")
        state["context"] = previous + "\n\nSEARCH: " + search_query + "\nRESULTS: " + results

        print(f"📚 发现信息，分析结果...")

        # 搜索后总是返回到决策节点
        return "decide"

class AnswerQuestion(Node):
    """问题回答节点 - 生成最终答案"""
    def exec(self, state):
        question = state["question"]
        context = state.get("context", "")

        print(f"✍️ 撰写最终答案...")

        # 为 LLM 创建回答问题的提示词
        prompt: md = f"""
### 上下文
基于以下信息，回答问题。
问题: {question}
研究: {context}

## 你的答案:
使用研究结果提供全面的答案。
"""
        # 调用 LLM 生成答案
        answer = call_llm(prompt)
        # 在共享存储中保存答案
        state["answer"] = answer
        print(f"✅ 答案生成成功")


def main(question: str):
    from agnflow.core import Flow

    decide = DecideAction()
    search = SearchWeb()
    answer = AnswerQuestion()
    flow = Flow()
    flow[decide >> search >> decide >> answer]

    state = {"question": question}
    print(f"🤔 Processing question: {question}")
    flow.run(state)
    print("\n🎯 Final Answer:")
    print(state.get("answer", "No answer found"))


if __name__ == "__main__":
    main("谁获得2024年诺贝尔物理学奖？")

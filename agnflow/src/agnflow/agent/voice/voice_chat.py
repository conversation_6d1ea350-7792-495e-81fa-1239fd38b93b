"""语音对话节点实现。

本模块提供了完整的语音对话流程节点，包括音频捕获、语音转文本、LLM 对话和文本转语音功能。
演示了如何构建端到端的语音交互系统。
"""

import scipy.io.wavfile
import io
import soundfile  # For converting MP3 bytes to NumPy array

from agnflow.core import Node
from agnflow.agent.voice.audio_utils import record_audio, play_audio_data
from agnflow.agent.voice.speech_to_text import speech_to_text_api
from agnflow.agent.voice.text_to_speech import text_to_speech_api
from agnflow.agent.llm import call_llm

class CaptureAudioNode(Node):
    """音频捕获节点 - 使用 VAD 录制用户音频输入"""

    def exec(self, state):  # prep_res is not used as per design
        print("\n开始录音...")
        audio_data, sample_rate = record_audio()
        if audio_data is None:
            return None, None

        """后处理阶段：保存音频数据或处理捕获失败"""
        audio_numpy_array = audio_data
        if audio_numpy_array is None:
            state["user_audio_data"] = None
            state["user_audio_sample_rate"] = None
            print("CaptureAudioNode: 录音失败。")
            return "end_conversation"

        state["user_audio_data"] = audio_numpy_array
        state["user_audio_sample_rate"] = sample_rate
        print(f"Audio captured ({len(audio_numpy_array)/sample_rate:.2f}s), proceeding to STT.")


class SpeechToTextNode(Node):
    """语音转文本节点 - 将录制的内存音频转换为文本"""

    def exec(self, state):
        """准备阶段：获取音频数据和采样率"""
        user_audio_data = state.get("user_audio_data")
        user_audio_sample_rate = state.get("user_audio_sample_rate")
        if user_audio_data is None or user_audio_sample_rate is None:
            print("SpeechToTextNode: 没有音频数据要处理。")
            return None  # Signal to skip exec

        """执行阶段：调用语音转文本 API"""
        if user_audio_data is None:
            return None  # 如果没有音频数据，则跳过执行

        audio_numpy_array, sample_rate = user_audio_data, user_audio_sample_rate

        # 将 NumPy 数组转换为 WAV 字节用于 API
        byte_io = io.BytesIO()
        scipy.io.wavfile.write(byte_io, sample_rate, audio_numpy_array)
        wav_bytes = byte_io.getvalue()

        print("将语音转换为文本...")
        transcribed_text = speech_to_text_api(audio_data=wav_bytes, sample_rate=sample_rate)

        """后处理阶段：保存转录文本到对话历史"""
        if transcribed_text is None:
            print("SpeechToTextNode: STT API 返回了空文本。")
            return "end_conversation"

        print(f"User: {transcribed_text}")

        if "chat_history" not in state:
            state["chat_history"] = []
        state["chat_history"].append({"role": "user", "content": transcribed_text})

        state["user_audio_data"] = None
        state["user_audio_sample_rate"] = None
        return "default"


class QueryLLMNode(Node):
    """LLM 查询节点 - 从 LLM 获取响应"""

    def exec(self, state):
        """准备阶段：获取对话历史"""
        chat_history = state.get("chat_history", [])

        if not chat_history:
            print("QueryLLMNode: Chat history is empty. Skipping LLM call.")
            return None

        """执行阶段：调用 LLM API"""
        if chat_history is None:
            return None

        chat_history = chat_history
        print("Sending query to LLM...")
        llm_response_text = call_llm(messages=chat_history)

        """后处理阶段：保存 LLM 响应到对话历史"""
        if llm_response_text is None:
            print("QueryLLMNode: LLM API 返回了空响应。")
            return "end_conversation"

        llm_response_text = llm_response_text
        print(f"LLM: {llm_response_text}")

        state["chat_history"].append({"role": "assistant", "content": llm_response_text})
        return "default"


class TextToSpeechNode(Node):
    """文本转语音节点 - 将 LLM 的文本响应转换为语音并播放"""

    def exec(self, state):
        """准备阶段：获取对话历史中的最后一条助手消息"""
        chat_history = state.get("chat_history", [])
        if not chat_history:
            print("TextToSpeechNode: 对话历史为空。没有 LLM 响应要合成。")
            return None

        last_message = chat_history[-1]
        if last_message.get("role") == "assistant" and last_message.get("content"):
            llm_text_response = last_message.get("content")
        else:
            print("TextToSpeechNode: 最后一条消息不是助手消息或没有内容。跳过 TTS。")
            llm_text_response = None

        """执行阶段：调用文本转语音 API"""
        if llm_text_response is None:
            return None, None
        llm_text_response = last_message.get("content")
        print("将 LLM 响应转换为语音...")
        llm_audio_bytes, llm_sample_rate = text_to_speech_api(llm_text_response)

        """后处理阶段：播放音频并决定是否继续对话"""
        if llm_audio_bytes is None:
            print("TextToSpeechNode: TTS 失败或被跳过")
            return "capture_audio"

        llm_audio_bytes, llm_sample_rate = llm_audio_bytes, llm_sample_rate

        print("🔊 播放 LLM 响应...")
        try:
            audio_segment, sr_from_file = soundfile.read(io.BytesIO(llm_audio_bytes))
            play_audio_data(audio_segment, sr_from_file)
        except Exception as e:
            print(f"🔊 播放 TTS 音频失败: {e}")
            return "capture_audio"

        if state.get("continue_conversation", True):
            return "capture_audio"
        else:
            print("👋 对话结束")
            return "end_conversation"


if __name__ == "__main__":
    from agnflow.core import Flow

    capture_audio = CaptureAudioNode()
    speech_to_text = SpeechToTextNode()
    query_llm = QueryLLMNode()
    text_to_speech = TextToSpeechNode()
    voice_chat_flow = Flow()

    voice_chat_flow[capture_audio >> speech_to_text >> query_llm >> text_to_speech >> capture_audio]

    state = {
        "user_audio_data": None,
        "user_audio_sample_rate": None,
        "chat_history": [],
        "continue_conversation": True, 
    }

    voice_chat_flow.run(state)

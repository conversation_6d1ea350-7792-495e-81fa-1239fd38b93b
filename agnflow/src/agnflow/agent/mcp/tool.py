import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from dotenv import load_dotenv

load_dotenv()

# 全局标志，控制是否使用 MCP 或本地实现
MCP = False


def get_tools(server_script_path=None):
    """获取可用工具，根据 MCP 全局设置选择从 MCP 服务器或本地获取。"""
    if MCP:
        return mcp_get_tools(server_script_path)
    else:
        return local_get_tools(server_script_path)


def mcp_get_tools(server_script_path):
    """从 MCP 服务器获取可用工具。"""

    async def _get_tools():
        server_params = StdioServerParameters(command="python", args=[server_script_path])

        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                tools_response = await session.list_tools()
                return tools_response.tools

    return asyncio.run(_get_tools())


def local_get_tools(server_script_path=None):
    """本地获取工具的简单实现（不使用 MCP）。"""
    tools = [
        {
            "name": "add",
            "description": "将两个数字相加",
            "inputSchema": {"properties": {"a": {"type": "integer"}, "b": {"type": "integer"}}, "required": ["a", "b"]},
        },
        {
            "name": "subtract",
            "description": "用 a 减去 b",
            "inputSchema": {"properties": {"a": {"type": "integer"}, "b": {"type": "integer"}}, "required": ["a", "b"]},
        },
        {
            "name": "multiply",
            "description": "将两个数字相乘",
            "inputSchema": {"properties": {"a": {"type": "integer"}, "b": {"type": "integer"}}, "required": ["a", "b"]},
        },
        {
            "name": "divide",
            "description": "用 a 除以 b",
            "inputSchema": {"properties": {"a": {"type": "integer"}, "b": {"type": "integer"}}, "required": ["a", "b"]},
        },
    ]

    class DictObject(dict):
        """一个既可以作为字典也可以作为对象属性访问的简单类。"""

        def __init__(self, data):
            super().__init__(data)
            for key, value in data.items():
                if isinstance(value, dict):
                    self[key] = DictObject(value)
                elif isinstance(value, list) and value and isinstance(value[0], dict):
                    self[key] = [DictObject(item) for item in value]

        def __getattr__(self, key):
            try:
                return self[key]
            except KeyError:
                raise AttributeError(f"'DictObject' object has no attribute '{key}'")

    return [DictObject(tool) for tool in tools]


def format_tool_info(tools):
    # 格式化工具信息，供后续使用
    tool_info = []
    for i, tool in enumerate(tools, 1):
        properties = tool.inputSchema.get('properties', {})
        required = tool.inputSchema.get('required', [])
        
        params = []
        for param_name, param_info in properties.items():
            param_type = param_info.get('type', 'unknown')
            req_status = "(必填)" if param_name in required else "(选填)"
            params.append(f"    - {param_name} ({param_type}): {req_status}")
        
        tool_info.append(f"[{i}] {tool.name}\n  描述: {tool.description}\n  参数:\n" + "\n".join(params))
    return "\n".join(tool_info)

def call_tool(server_script_path=None, tool_name=None, arguments=None):
    """调用工具，根据 MCP 全局设置选择从 MCP 服务器或本地调用。"""
    if MCP:
        return mcp_call_tool(server_script_path, tool_name, arguments)
    else:
        return local_call_tool(server_script_path, tool_name, arguments)


def mcp_call_tool(server_script_path=None, tool_name=None, arguments=None):
    """在 MCP 服务器上调用工具。"""

    async def _call_tool():
        server_params = StdioServerParameters(command="python", args=[server_script_path])

        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                result = await session.call_tool(tool_name, arguments)
                return result.content[0].text

    return asyncio.run(_call_tool())


def local_call_tool(server_script_path=None, tool_name=None, arguments=None):
    """本地调用工具的简单实现（不使用 MCP）。"""
    # 工具的简单实现
    if tool_name == "add":
        if "a" in arguments and "b" in arguments:
            return arguments["a"] + arguments["b"]
        else:
            return "错误：缺少必要参数 'a' 或 'b'"
    elif tool_name == "subtract":
        if "a" in arguments and "b" in arguments:
            return arguments["a"] - arguments["b"]
        else:
            return "错误：缺少必要参数 'a' 或 'b'"
    elif tool_name == "multiply":
        if "a" in arguments and "b" in arguments:
            return arguments["a"] * arguments["b"]
        else:
            return "错误：缺少必要参数 'a' 或 'b'"
    elif tool_name == "divide":
        if "a" in arguments and "b" in arguments:
            if arguments["b"] == 0:
                return "错误：不允许除以零"
            return arguments["a"] / arguments["b"]
        else:
            return "错误：缺少必要参数 'a' 或 'b'"
    else:
        return f"错误：未知工具 '{tool_name}'"


if __name__ == "__main__":

    # 查找可用工具
    tools = get_tools("agnflow/src/agnflow/utils/mcp_server.py")
    print(format_tool_info(tools))

    # # 以美观的格式打印工具信息
    # for i, tool in enumerate(tools, 1):
    #     print(f"\n工具 {i}: {tool.name}")
    #     print("=" * (len(tool.name) + 8))
    #     print(f"描述: {tool.description}")

    #     # 参数部分
    #     print("参数:")
    #     properties = tool.inputSchema.get('properties', {})
    #     required = tool.inputSchema.get('required', [])

    #     # 无参数情况
    #     if not properties:
    #         print("  无")

    #     # 打印每个参数及其详细信息
    #     for param_name, param_info in properties.items():
    #         param_type = param_info.get('type', 'unknown')
    #         req_status = "(必填)" if param_name in required else "(选填)"
    #         print(f"  • {param_name}: {param_type} {req_status}")

    # # 调用工具
    # print("\n=== 调用 add 工具 ===")
    # a, b = 5, 3
    # result = call_tool("cookbook/pocketflow-mcp/simple_server.py", "add", {"a": a, "b": b})
    # print(f"{a} + {b} 的结果 = {result}")

    # # 你可以用不同参数再次调用
    # a, b = 10, 20
    # result = call_tool("cookbook/pocketflow-mcp/simple_server.py", "add", {"a": a, "b": b})
    # print(f"{a} + {b} 的结果 = {result}")

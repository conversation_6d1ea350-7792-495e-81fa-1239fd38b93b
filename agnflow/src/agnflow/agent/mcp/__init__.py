import yaml
import sys

from agnflow.core import Node, Flow
from agnflow.agent.llm import call_llm
from agnflow.agent.mcp.tool import get_tools, call_tool

class GetToolsNode(Node):

    def exec(self, state):
        # 初始化并获取工具
        # 问题现在通过 state 从 main 传递过来
        print("🔍 获取可用工具...")
        server_path = "simple_server.py"

        # 从 MCP 服务器获取工具
        tools = get_tools(server_path)

        # 保存工具并处理到决策节点
        state["tools"] = tools

        # 格式化工具信息，供后续使用
        tool_info = []
        for i, tool in enumerate(tools, 1):
            properties = tool.inputSchema.get("properties", {})
            required = tool.inputSchema.get("required", [])

            params = []
            for param_name, param_info in properties.items():
                param_type = param_info.get("type", "unknown")
                req_status = "(必填)" if param_name in required else "(选填)"
                params.append(f"    - {param_name} ({param_type}): {req_status}")

            tool_info.append(f"[{i}] {tool.name}\n  描述: {tool.description}\n  参数:\n" + "\n".join(params))

        state["tool_info"] = "\n".join(tool_info)
        return "decide"


class DecideToolNode(Node):

    def exec(self, state):
        # 为 LLM 处理问题准备提示词
        tool_info = state["tool_info"]
        question = state["question"]

        prompt = f"""
### 上下文
你是一个可以通过 Model Context Protocol (MCP) 使用工具的助手。

### 可用操作
{tool_info}

### 任务
请回答这个问题: "{question}"

## 下一步操作
分析问题，提取任何数字或参数，并决定使用哪个工具。
请用如下格式返回你的回复：

```yaml
thinking: |
    <你对问题的逐步推理，以及需要提取哪些数字>
tool: <你选择使用的工具名称>
reason: <你选择该工具的原因>
parameters:
    <参数名>: <参数值>
    <参数名>: <参数值>
```
重要提示：
1. 正确提取问题中的数字
2. 多行字段请使用 | 字符，并保持缩进为4个空格
3. 多行文本字段请使用 | 字符
"""

        # 调用 LLM 处理问题并决定使用哪个工具
        print("🤔 分析问题并决定使用哪个工具...")
        response = call_llm(prompt)

        # 从 YAML 中提取决策并保存到 state 上下文
        try:
            yaml_str = response.split("```yaml")[1].split("```")[0].strip()
            decision = yaml.safe_load(yaml_str)

            state["tool_name"] = decision["tool"]
            state["parameters"] = decision["parameters"]
            state["thinking"] = decision.get("thinking", "")

            print(f"💡 选择的工具: {decision['tool']}")
            print(f"🔢 提取的参数: {decision['parameters']}")

            return "execute"
        except Exception as e:
            print(f"❌ 解析 LLM 响应时出错: {e}")
            print("原始响应:", response)
            return None


class ExecuteToolNode(Node):

    def exec(self, state):
        # 准备工具执行参数
        inputs = state["tool_name"], state["parameters"]

        # 执行所选工具
        tool_name, parameters = inputs
        print(f"🔧 正在用参数 {parameters} 执行工具 '{tool_name}'")
        result = call_tool("simple_server.py", tool_name, parameters)

        print(f"\n✅ 最终答案: {result}")
        return "done"


if __name__ == "__main__":
    # 默认问题
    default_question = "What is 982713504867129384651 plus 73916582047365810293746529?"

    # 如果命令行有 --，则用命令行问题
    question = default_question
    for arg in sys.argv[1:]:
        if arg.startswith("--"):
            question = arg[2:]
            break

    print(f"🤔 正在处理问题: {question}")

    # 创建节点
    get_tools_node = GetToolsNode()
    decide_node = DecideToolNode()
    execute_node = ExecuteToolNode()

    # 连接节点
    get_tools_node >> "decide" >> decide_node
    decide_node >> "execute" >> execute_node

    # 创建并运行流程
    flow = Flow(start=get_tools_node)
    state = {"question": question}
    flow.run(state)

from fastmcp import FastMCP

mcp = FastMCP("数学运算 Server")

@mcp.tool()
def add(a: int, b: int) -> int:
    """将两个数字相加"""
    return a + b

@mcp.tool()
def subtract(a: int, b: int) -> int:
    """用 a 减去 b"""
    return a - b

@mcp.tool()
def multiply(a: int, b: int) -> int:
    """将两个数字相乘"""
    return a * b

@mcp.tool()
def divide(a: int, b: int) -> float:
    """用 a 除以 b"""
    if b == 0:
        raise ValueError("不允许除以零")
    return a / b

if __name__ == "__main__":
    mcp.run()

chat:     ## 启动 agnchat 的开发服务器
	cd agnchat && npm run dev 

flow:     ## 启动 agnflow 聊天机器人后端（简易版）
	cd agnflow && uv run python src/agnflow/chatbot/server.py

example:  ## 运行 agnflow 聊天机器人示例
	cd agnflow && uv run python examples/chatbot/main.py

dev:      ## 并行启动 chat 和 flow
	make -j2 chat flow 

test:     ## 并行启动 chat 和 example
	make -j2 chat example

help: ## 显示帮助信息
	@echo "可用的命令:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST)  | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

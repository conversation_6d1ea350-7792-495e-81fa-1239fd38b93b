# 使用 Debian 基础镜像
FROM debian:latest as builder

# 设置环境变量，避免在构建过程中出现交互式提示
ENV DEBIAN_FRONTEND=noninteractive

# 更新系统并安装构建工具和依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    pkg-config \
    libjson-c-dev \
    libwebsockets-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# 克隆 ttyd 源码并编译安装
RUN git clone https://github.com/tsl0922/ttyd.git /ttyd && \
    cd /ttyd && \
    mkdir build && \
    cd build && \
    cmake .. && \
    make && \
    make install

FROM ghcr.io/astral-sh/uv:debian-slim

COPY --from=builder /usr/local/bin/ttyd /bin/ttyd

RUN apt-get update && apt-get install -y \
    libjson-c5 \
    libwebsockets-dev \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
ENV UV_COMPILE_BYTECODE=1
ENV UV_LINK_MODE=copy

RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv sync --frozen --no-install-project --no-dev

ADD . /app
COPY docker/entrypoint.sh /app/entrypoint.sh
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-dev

EXPOSE 80

ENTRYPOINT ["/app/entrypoint.sh"]

# Python插件
爱派支持使用Python编程语言创建插件

## 插件位置
Python 插件的目录为配置文件里 `plugin_dir` 指定的目录或默认配置的`~/.aipyapp/plugins`目录

- UNIX / Mac上：`~/.aipyapp/plugins`目录
- Windows：`C:\Users\<USER>\.aipyapp\plugins` 目录

插件开发具有以下规范要求：
 - 每个插件为一个 Python 文件
 - 文件名必须以 `.py` 结尾，且不以 `_` 开头。
 - 插件必须包含一个 `Plugin` 类
 - 插件的方法实现必须是全局对象 [event_bus](Event.md) 中支持的 Event 类型


## 插件接口示例

插件`Plugin` 类，接口定义如下：  

```python
class Plugin:
    def on_task_start(self, prompt):
        """
        任务开始事件
        param: prompt
        """
        pass

    def on_exec(self, blocks):
        """
        执行代码事件
        param: blocks
        """
        pass

    def on_result(self, result):
        """
        返回结果事件
        param: result
        """
        pass

    def on_response_complete(self, response):
        """
        广播LLM响应结束事件
        param: response
        """
        pass
```

## 样例插件
### 代码保存插件
在插件plugins 目录创建 save_code.py 文件 （`~/.aipyapp/plugins/code_save.py`）, 插件会在每次 LLM 生成的 python 代码被执行时自动保存代码到工作目录。
插件代码如下：

```python
import os
import datetime
from pathlib import Path


class Plugin:
    def __init__(self):
        print("[+] 加载爱派代码保存插件")

    def on_exec(self, blocks):
        """
        执行代码事件
        param: blocks
        """
        code_block = blocks['main']
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        filename = f"{timestamp}.py"
        directory = Path.cwd()
        file_path = os.path.join(directory, filename)
        print(f"[i] plugin - Save code to file {file_path}")

        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(code_block)
        except Exception as e:
            print(f"[!] plugin - Save code file error: {e}")
```

### 生成小红书封面插件
在插件plugins 目录创建 xiaohongshu.py 文件 （`~/.aipyapp/plugins/xiaohongshu.py`）, 插件会在用户输入的任务之前插入这段小红书封面生成的提示词模板。这样 AI 就能根据用户输入的爱派任务要求自动生成小红书文案的封面，不用在爱派任务框输入这段超长的提示词。
插件代码如下：

```python
class Plugin:
	def __init__(self):
		print("[+] 加载爱派小红书封面生成插件")
	def on_task_start(self, prompt):
		"""
		任务开始事件
		param: prompt
		"""
		task = prompt.get('task')
		role = """你是一位优秀的网页和营销视觉设计师，具有丰富的UI/UX设计经验，曾为众多知名品牌打造过引人注目的营销视觉，擅长将现代设计趋势与实用营销策略完美融合。现在需要为我创建一张专业级小红书封面。请使用HTML、CSS和JavaScript代码实现以下要求：

## 基本要求

**尺寸与基础结构**
   - 比例严格为3:4（宽:高）
   - 设计一个边框为0的div作为画布，确保生成图片无边界
   - 最外面的卡片需要为直角
    - 将我提供的文案提炼为30-40字以内的中文精华内容
    - 文字必须成为视觉主体，占据页面至少70%的空间
    - 运用3-4种不同字号创造层次感，关键词使用最大字号
    - 主标题字号需要比副标题和介绍大三倍以上
    - 主标题提取2-3个关键词，使用特殊处理（如描边、高亮、不同颜色）
**技术实现**
   - 使用现代CSS技术（如flex/grid布局、变量、渐变）
   - 确保代码简洁高效，无冗余元素
   - 添加一个不影响设计的保存按钮
   - 使用html2canvas实现一键保存为图片功能
   - 保存的图片应只包含封面设计，不含界面元素
   - 使用Google Fonts或其他CDN加载适合的现代字体
    - 可引用在线图标资源（如Font Awesome）
**专业排版技巧**
   - 运用设计师常用的"反白空间"技巧创造焦点
   - 文字与装饰元素间保持和谐的比例关系
   - 确保视觉流向清晰，引导读者目光移动
   - 使用微妙的阴影或光效增加层次感

## 设计风格

- **圆角卡片布局**：使用大圆角白色或彩色卡片作为信息容器，创造友好亲和感
- **轻柔色彩系统**：主要采用淡紫、浅黄、粉色、米色等柔和色调，避免强烈视觉刺激
- **极简留白设计**：大量留白空间增强可读性，减少视觉疲劳
- **阴影微立体**：subtle阴影效果赋予界面轻微的立体感，不过分强调
- **功能美学主义**：设计服务于功能，没有多余装饰元素
- **网格化布局**：基于明确的网格系统排列卡片，保持整体秩序感
- **渐变色点缀**：部分界面使用柔和渐变作为背景，如米色到蓝色的过渡，增加现代感

## 文字排版风格

- **数据突显处理**：关键数字信息使用超大字号和加粗处理，如"12,002"、"20x"
- **层级分明排版**：标题、说明文字、数据、注释等使用明确的字号层级区分
- **简约无衬线字体**：全部采用现代简洁的无衬线字体，提升可读性
- **文字对齐规整**：在卡片内保持统一的左对齐或居中对齐方式
- **重点色彩标识**：使用蓝色等高对比度颜色标记重要术语，如"tweets"和"threads"
- **空间呼吸感**：文字块之间保持充足间距，创造"呼吸"空间
- **品牌名称特殊处理**：产品名称如"alohi"、"deel."采用特殊字体或风格，强化品牌识别

## 视觉元素风格

- **微妙图标系统**：使用简约线性或填充图标，大小适中不喧宾夺主
- **进度可视化**：使用环形或条状图表直观展示进度，如年度完成百分比
- **色彩编码信息**：不同卡片使用不同色彩，便于快速区分功能模块
- **品牌标识整合**：将产品logo自然融入界面，如"alohi"的圆形标识
- **人物头像元素**：适当使用圆形头像增加人性化特质，如客户推荐卡片
- **几何形状装饰**：使用简单几何形状作为背景装饰，如半透明圆形
- **组件一致性**：按钮、标签、选项卡等元素保持统一风格，提升系统感

## 用户输入内容
   用户会会提供文案内容初稿。请根据内容提炼生成。
"""
		prompt['task'] = f"{role}\n{task}"
```
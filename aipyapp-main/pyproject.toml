[project]
name = "aipyapp"
version = "0.1.0"
# dynamic = ["version"]
description = "AIPyApp: AI-Powered Python & Python-Powered AI"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
  "anthropic>=0.49.0",
  "beautifulsoup4>=4.13.3",
  "dynaconf>=3.2.10",
  "openai>=1.68.2",
  "pandas>=2.2.3",
  "prompt-toolkit>=3.0.50",
  "pygments>=2.19.1",
  "requests>=2.32.3",
  "rich>=13.9.4",
  "seaborn>=0.13.2",
  "term-image>=0.7.2",
  "tomli-w>=1.2.0",
  "qrcode>=8.1",
  "loguru>=0.7.3",
  "questionary>=2.1.0",
  "mcp[cli]>=1.10.0",
  "openpyxl>=3.1.5",
  "pyyaml>=6.0.2",
  "charset-normalizer>=3.4.2",
  "wxpython>=4.2.3",
]

[project.scripts]
aipy = "aipyapp.__main__:main"

[project.gui-scripts]
aipyw = "aipyapp.__main__:mainw"

[build-system]
requires = ["hatchling", "hatch-vcs>=0.5.0"]
build-backend = "hatchling.build"

[tool.hatch.version]
source = "vcs"
raw-options = { local_scheme = "no-local-version", version_scheme = "only-version" }

[tool.hatch.build.hooks.vcs]
version-file = "aipyapp/__version__.py"

[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple/"
default = false

[tool.ruff]
# 启用格式化和 lint 规则
select = ["E", "F"]
ignore = []
line-length = 88

[tool.ruff.format]
quote-style = "preserve"
indent-style = "space"
line-ending = "auto"
# 跳过特定字符串格式化
skip-magic-trailing-comma = true

[tool.ruff.lint]
# 启用 isort 规则
extend-select = []

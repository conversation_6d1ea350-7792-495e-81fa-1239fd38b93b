name: Docker Image CI

on:
  push:
    tags:
      - "v*"
    paths:
      - 'docker/Dockerfile'
      - 'docker/Dockerfile.deb'
      - 'aipyapp/**'
      - 'pyproject.toml'
  workflow_dispatch:

jobs:
  build-amd64:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build amd64 Image
        uses: docker/build-push-action@v3
        with:
          platforms: linux/amd64
          push: true
          file: docker/Dockerfile
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/aipy:amd64-latest

  build-arm64:
    needs: build-amd64  # 确保在 amd64 构建完成后执行
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2  # 设置 ARM 架构支持

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build arm64 Image
        uses: docker/build-push-action@v3
        with:
          platforms: linux/arm64
          push: true
          file: docker/Dockerfile.deb
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/aipy:arm64-latest

  manifest:
    needs: [build-amd64, build-arm64]
    runs-on: ubuntu-latest
    steps:
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Create and Push Manifest List
        run: |
          docker manifest create ${{ secrets.DOCKERHUB_USERNAME }}/aipy:latest \
            --amend ${{ secrets.DOCKERHUB_USERNAME }}/aipy:amd64-latest \
            --amend ${{ secrets.DOCKERHUB_USERNAME }}/aipy:arm64-latest

          docker manifest push ${{ secrets.DOCKERHUB_USERNAME }}/aipy:latest

name: Build and Publish Python Package

on:
  push:
    tags:
      - "v*"
  workflow_dispatch:

permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v5

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version-file: "pyproject.toml"
          
      - name: Install the project
        run: uv sync --all-extras

      - name: Build Package
        run: uv build

      - name: Upload Package as Artifact
        uses: actions/upload-artifact@v4
        with:
          name: python-package
          path: dist/*

      - name: Upload to GitHub Releases
        uses: softprops/action-gh-release@v2
        with:
          files: dist/*
          token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Publish to PyPI
        env:
          UV_PUBLISH_TOKEN: ${{ secrets.PYPI_TOKEN }}
        run: uv publish
from collections import Counter, defaultdict
from typing import NamedTuple, TYPE_CHECKING
from loguru import logger
from rich.live import Live
from rich.text import Text

from .. import T, __respath__
from .plugin import event_bus
from ..llm import CLIENTS, ChatMessage, ModelRegistry, ModelCapability, BaseClient
from .multimodal import LLMContext
if TYPE_CHECKING:
    from .config import Settings

class ChatHistory:
    """管理聊天历史记录和统计信息"""
    def __init__(self):
        self.messages = []
        self._total_tokens = Counter()

    def __len__(self):
        return len(self.messages)
    
    def json(self):
        return [msg.__dict__ for msg in self.messages]

    def add(self, role, content):
        self.add_message(ChatMessage(role=role, content=content))

    def add_message(self, message: ChatMessage):
        self.messages.append(message)
        self._total_tokens += message.usage

    def get_usage(self):
        return iter(row.usage for row in self.messages if row.role == "assistant")
    
    def get_summary(self):
        summary = {'time': 0, 'input_tokens': 0, 'output_tokens': 0, 'total_tokens': 0}
        summary.update(dict(self._total_tokens))
        summary['rounds'] = sum(1 for row in self.messages if row.role == "assistant")
        return summary

    def get_messages(self):
        return [{"role": msg.role, "content": msg.content} for msg in self.messages]

class LineReceiver(list):
    """流式内容按行级显示"""
    def __init__(self):
        super().__init__()
        self.buffer = ""

    @property
    def content(self):
        return '\n'.join(self)
    
    def feed(self, data: str):
        self.buffer += data
        new_lines = []

        while '\n' in self.buffer:
            line, self.buffer = self.buffer.split('\n', 1)
            self.append(line)
            new_lines.append(line)

        return new_lines
    
    def empty(self) -> bool:
        return not self and not self.buffer
    
    def done(self) -> str:
        buffer = self.buffer
        if buffer:
            self.append(buffer)
            self.buffer = ""
        return buffer

class LiveManager:
    """实时显示LLM响应过程"""
    def __init__(self, name, quiet=False):
        self.live = None
        self.name = name
        self.lr = LineReceiver()
        self.lr_reason = LineReceiver()
        self.title = f"{self.name} {T('Reply')}"
        self.reason_started = False
        self.display_lines = []
        self.max_lines = 10
        self.quiet = quiet

    @property
    def content(self):
        return self.lr.content
    
    @property
    def reason(self):
        return self.lr_reason.content
    
    def __enter__(self):
        if self.quiet: return self
        self.live = Live(auto_refresh=False, vertical_overflow='crop', transient=True)
        self.live.__enter__()
        return self

    def process_chunk(self, content, *, reason=False):
        if not content: return
 
        if not reason and self.lr.empty() and not self.lr_reason.empty():
            line = self.lr_reason.done()
            event_bus.broadcast('response_stream', {'llm': self.name, 'content': f"{line}\n\n----\n\n", 'reason': True})

        lr = self.lr_reason if reason else self.lr
        lines = lr.feed(content)
        if not lines: return

        lines2 = [line for line in lines if not line.startswith('<!-- Block-') and not line.startswith('<!-- Cmd-')]
        if lines2:
            content = '\n'.join(lines2)
            event_bus.broadcast('response_stream', {'llm': self.name, 'content': content, 'reason': reason})

        if self.quiet: return

        if reason and not self.reason_started:
            self.display_lines.append("<think>")
            self.reason_started = True
        elif not reason and self.reason_started:
            self.display_lines.append("</think>")
            self.reason_started = False

        self.display_lines.extend(lines)
        while len(self.display_lines) > self.max_lines:
            self.display_lines.pop(0)
        content = '\n'.join(self.display_lines)
        self.live.update(Text(content, style="dim white"), refresh=True)

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.lr.buffer:
            self.process_chunk('\n')
        if self.live:
            self.live.__exit__(exc_type, exc_val, exc_tb)

class LLMRecord(NamedTuple):
    Name: str
    Model: str
    Max_Tokens: int
    Base_URL: str

class ClientManager:
    """管理多个LLM客户端"""
    MAX_TOKENS = 8192

    def __init__(self, settings):
        self.clients: dict[str, BaseClient] = {}
        self.default: BaseClient | None = None
        self.current: BaseClient | None = None
        self.log = logger.bind(src='client_manager')
        self.names = self._init_clients(settings)
        self.model_registry = ModelRegistry(__respath__ / "models.yaml")

    def _create_client(self, config: dict) -> BaseClient | None:
        kind = config.get("type", "openai")
        client_class = CLIENTS.get(kind.lower())
        if not client_class:
            self.log.error('Unsupported LLM provider', kind=kind)
            return None
        return client_class(config)

    def _init_clients(self, settings: "Settings") -> dict[str, set[str]]:
        names = defaultdict(set)
        max_tokens = settings.get('max_tokens', self.MAX_TOKENS)
        for name, config in settings.llm.items():
            if not config.get('enable', True):
                names['disabled'].add(name)
                continue

            config['name'] = name
            try:
                client: BaseClient = self._create_client(config)
            except Exception as e:
                self.log.exception('Error creating LLM client', config=config)
                names['error'].add(name)
                continue

            if not client or not client.usable():
                names['disabled'].add(name)
                self.log.error('LLM client not usable', name=name, config=config)
                continue

            names['enabled'].add(name)
            if not client.max_tokens:
                client.max_tokens = max_tokens
            self.clients[name] = client

            if config.get('default', False) and not self.default:
                self.default = client
                names['default'] = name

        if not self.default:
            name = list(self.clients.keys())[0]
            self.default = self.clients[name]
            names['default'] = name

        self.current = self.default
        return names

    def __len__(self):
        return len(self.clients)

    def __repr__(self):
        return f"Current: {'default' if self.current == self.default else self.current}, Default: {self.default}"

    def __contains__(self, name):
        return name in self.clients

    def use(self, name):
        client = self.clients.get(name)
        if client and client.usable():
            self.current = client
            return True
        return False

    def get_client(self, name):
        return self.clients.get(name)

    def Client(self):
        return Client(self)

    def to_records(self) -> list[LLMRecord]:
        rows: list[LLMRecord] = []
        for name, client in self.clients.items():
            rows.append(LLMRecord(name, client.model, client.max_tokens, client.base_url))
        return rows

    def get_model_info(self, model: str):
        return self.model_registry.get_model_info(model)

class Client:
    def __init__(self, manager: ClientManager):
        self.manager = manager
        self.current = manager.current
        self.history = ChatHistory()
        self.log = logger.bind(src='client', name=self.current.name)

    @property
    def name(self):
        return self.current.name
    
    def use(self, name):
        client = self.manager.get_client(name)
        if client and client.usable():
            self.current = client
            self.log = logger.bind(src='client', name=self.current.name)
            return True
        return False
    
    def has_capability(self, content: LLMContext) -> bool:
        # 判断 content 需要什么能力
        if isinstance(content, str):
            return True
        
        #TODO: 不应该硬编码字符串
        if self.current.kind == 'trust':
            return True
        
        model = self.current.model
        model = model.rsplit('/', 1)[-1]
        model_info = self.manager.get_model_info(model)
        if not model_info:
            self.log.error(f"Model info not found for {model}")
            return False
                
        capabilities = set()
        for item in content:
            if item['type'] == 'image_url':
                capabilities.add(ModelCapability.IMAGE_INPUT)
            if item['type'] == 'file':
                capabilities.add(ModelCapability.FILE_INPUT)
            if item['type'] == 'text':
                capabilities.add(ModelCapability.TEXT)
        
        return any(capability in model_info.capabilities for capability in capabilities)
    
    def __call__(self, content: LLMContext, *, system_prompt=None, quiet=False):
        client = self.current
        stream_processor = LiveManager(client.name, quiet=quiet)
        msg = client(self.history, content, system_prompt=system_prompt, stream_processor=stream_processor)
        if msg:
            event_bus.broadcast('response_complete', {'llm': client.name, 'content': msg})
        else:
            self.log.error(f"LLM: {client.name} response is None")
        return msg

if __name__ == '__main__':
    # 示例：演示 LLM 模块的基本功能

    # 模拟配置设置
    class MockSettings:
        def __init__(self):
            self.llm = {
                "openai": {
                    "type": "openai",
                    "api_key": "your-api-key-here",
                    "model": "gpt-3.5-turbo",
                    "base_url": "https://api.openai.com/v1",
                    "enable": True,
                    "default": True
                }
            }

        def get(self, key, default=None):
            return getattr(self, key, default)

    def demo_chat_history():
        """演示聊天历史记录功能"""
        print("=== 聊天历史记录演示 ===")

        # 创建聊天历史记录实例
        history = ChatHistory()

        # 添加消息
        history.add("user", "你好，请介绍一下Python")
        history.add("assistant", "Python是一种高级编程语言...")
        history.add("user", "Python有哪些特点？")
        history.add("assistant", "Python具有简洁易读、跨平台、丰富的库等特点...")

        # 显示历史记录信息
        print(f"消息数量: {len(history)}")
        print(f"对话轮数: {history.get_summary()['rounds']}")
        print("消息列表:")
        for i, msg in enumerate(history.get_messages(), 1):
            print(f"  {i}. {msg['role']}: {msg['content'][:50]}...")
        print()

    def demo_line_receiver():
        """演示行接收器功能"""
        print("=== 行接收器演示 ===")

        # 创建行接收器实例
        receiver = LineReceiver()

        # 模拟流式数据输入
        test_data = "第一行\n第二行\n第三行部分"
        print(f"输入数据: {repr(test_data)}")

        # 处理数据
        new_lines = receiver.feed(test_data)
        print(f"新行: {new_lines}")
        print(f"缓冲区: {repr(receiver.buffer)}")

        # 完成输入
        remaining = receiver.done()
        print(f"剩余数据: {repr(remaining)}")
        print(f"完整内容: {repr(receiver.content)}")
        print()

    # def demo_client_manager():
    #     """演示客户端管理器功能"""
    #     print("=== 客户端管理器演示 ===")

    #     try:
    #         # 创建模拟设置
    #         settings = MockSettings()

    #         # 创建客户端管理器（注意：这里可能会因为缺少实际配置而失败）
    #         print("尝试创建客户端管理器...")
    #         print("注意：由于缺少实际的API配置，此演示可能无法完全运行")
    #         print(f"配置的LLM: {list(settings.llm.keys())}")

    #         # 显示LLM记录格式
    #         print("\nLLM记录格式示例:")
    #         sample_record = LLMRecord(
    #             Name="openai",
    #             Model="gpt-3.5-turbo",
    #             Max_Tokens=8192,
    #             Base_URL="https://api.openai.com/v1"
    #         )
    #         print(f"  {sample_record}")

    #     except Exception as e:
    #         print(f"客户端管理器创建失败: {e}")
    #         print("这是正常的，因为需要实际的API配置")
    #     print()

    def demo_live_manager():
        """演示实时管理器功能"""
        print("=== 实时管理器演示 ===")

        # 创建实时管理器（静默模式）
        live_mgr = LiveManager("demo-llm", quiet=True)

        # 模拟流式响应处理
        with live_mgr:
            # 模拟推理过程
            live_mgr.process_chunk("正在思考", reason=True)
            live_mgr.process_chunk("这个问题需要", reason=True)
            live_mgr.process_chunk("仔细分析\n", reason=True)

            # 模拟正式回复
            live_mgr.process_chunk("根据您的问题，")
            live_mgr.process_chunk("我认为Python是一种")
            live_mgr.process_chunk("非常优秀的编程语言\n")

        print(f"推理内容: {live_mgr.reason}")
        print(f"回复内容: {live_mgr.content}")
        print()

    # 运行所有演示
    print("LLM模块功能演示")
    print("=" * 50)

    demo_chat_history()
    demo_line_receiver()
    # demo_client_manager()
    demo_live_manager()

    print("演示完成！")
    print("\n使用说明:")
    print("1. ChatHistory: 管理对话历史和统计信息")
    print("2. LineReceiver: 处理流式文本数据的行缓冲")
    print("3. ClientManager: 管理多个LLM客户端")
    print("4. LiveManager: 实时显示LLM响应过程")
    print("5. Client: 提供统一的LLM调用接口")
from typing import TYPE_CHECKING, List, Dict, Any
import os
import json
import uuid
import time
from datetime import datetime
from collections import OrderedDict
from importlib.resources import read_text
from loguru import logger
from rich.rule import Rule
from rich.panel import Panel
from rich.align import Align
from rich.table import Table
from rich.syntax import Syntax
from rich.console import Console, Group
from rich.markdown import Markdown
from pathlib import Path

if TYPE_CHECKING:
    from .taskmgr import TaskManager, EnvRecord

from .. import T, __respkg__
from ..exec import BlockExecutor
from .llm import Client, ChatMessage
from .multimodal import MMContent, LLMContext
from .config import Settings
from .runtime import CliPythonRuntime
from .blocks import CodeBlocks, CodeBlock
from .plugin import event_bus
from .utils import get_safe_filename
from .interface import Stoppable
from .prompt import get_task_prompt, get_chat_prompt, get_results_prompt, get_mcp_result_prompt
from .mcp_tool import MCPToolManager

CONSOLE_WHITE_HTML = read_text(__respkg__, "console_white.html")
CONSOLE_CODE_HTML = read_text(__respkg__, "console_code.html")


from typing import NamedTuple


class TaskRecord(NamedTuple):
    task_id: str
    start_time: str
    done_time: str
    instruction: str


class Task(Stoppable):
    """任务类"""
    MAX_ROUNDS = 16

    def __init__(self, manager: "TaskManager") -> None:
        super().__init__()
        self.task_id: str = uuid.uuid4().hex
        self.log = logger.bind(src="task", id=self.task_id)

        self.manager: "TaskManager" = manager
        self.settings: Settings = manager.settings
        self.max_rounds: int = self.settings.get("max_rounds", self.MAX_ROUNDS)
        self.envs: Dict[str, EnvRecord] = manager.envs
        self.gui: bool = manager.gui
        self.console: Console = Console(file=manager.console.file, record=True)
        self.work_dir: Path = manager.cwd / self.task_id
        self.client: Client = None
        self.mcp: MCPToolManager | None = None

        self.instruction: str = None
        self.system_prompt: str = None
        self.start_time: float = None
        self.done_time: float = None
        self.saved: bool = None

        self.code_blocks: CodeBlocks = CodeBlocks(self.console)
        self.runtime: CliPythonRuntime = CliPythonRuntime(self)
        self.runner: BlockExecutor = BlockExecutor()
        self.runner.set_python_runtime(self.runtime)

    def use(self, name: str) -> bool:
        """切换使用的模型/客户端"""
        ret = self.client.use(name)
        # self.console.print('[green]Ok[/green]' if ret else '[red]Error[/red]')
        return ret

    # region: 文件保存和持久化
    def save_box(self, path: str) -> None:
        if self.console.record:
            self.console.save_html(path, clear=False, code_format=CONSOLE_WHITE_HTML)

    def save_html(self, path: str, task: dict) -> None:
        if "chats" in task and isinstance(task["chats"], list) and len(task["chats"]) > 0:
            if task["chats"][0]["role"] == "system":
                task["chats"].pop(0)

        task_json = json.dumps(task, ensure_ascii=False, default=str)
        html_content = CONSOLE_CODE_HTML.replace("{{code}}", task_json)
        try:
            with open(path, "w", encoding="utf-8") as f:
                f.write(html_content)
        except Exception as e:
            self.console.print_exception()

    def _auto_save(self) -> None:
        instruction = self.instruction
        task = OrderedDict()
        task["instruction"] = instruction
        task["start_time"] = self.start_time
        task["done_time"] = self.done_time
        task["chats"] = self.client.history.json()
        # task['envs'] = self.runtime.envs
        task["runner"] = self.runner.history
        task["blocks"] = self.code_blocks.to_list()

        filename = self.work_dir / "task.json"
        try:
            json.dump(task, open(filename, "w", encoding="utf-8"), ensure_ascii=False, indent=4, default=str)
        except Exception as e:
            self.log.exception("Error saving task")

        filename = self.work_dir / "console_code.html"
        self.save_html(filename, task)
        filename = self.work_dir / "console_white.html"
        self.save_box(filename)
        self.saved = True
        self.log.info("Task auto saved")

    # endregion

    # region: 任务生命周期管理
    def run(self, instruction: str) -> None:
        """
        执行自动处理循环，直到 LLM 不再返回代码消息
        instruction: 用户输入的字符串（可包含@file等多模态标记）
        """
        # 显示任务开始的提示框
        self.box(f"[yellow]{T('Start processing instruction')}", instruction, align="center")

        # 处理多模态内容
        mmc = MMContent(instruction, base_path=self.manager.cwd)
        try:
            content = mmc.content
        except Exception as e:
            self.console.print(f"[red]{e}[/red]")
            return

        # 检查当前模型是否支持该内容
        if not self.client.has_capability(content):
            self.console.print(f"[red]{T('Current model does not support this content')}[/red]")
            return

        # 首次运行时初始化相关参数
        if not self.start_time:
            self.start_time = time.time()
            self.instruction = instruction
            if isinstance(content, str):
                # 生成任务提示词
                content = get_task_prompt(content, gui=self.gui)
                event_bus("task_start", content)
                content = json.dumps(content, ensure_ascii=False, default=str)
            system_prompt = self.system_prompt
        else:
            # 非首次运行，生成对话提示词
            system_prompt = None
            if isinstance(content, str):
                content = get_chat_prompt(content, self.instruction)
                content = json.dumps(content, ensure_ascii=False, default=str)

        # 创建任务工作目录并切换到该目录
        self.work_dir.mkdir(exist_ok=True)
        os.chdir(self.work_dir)

        # 进入自动处理循环
        rounds = 1
        max_rounds = self.max_rounds
        self.saved = False
        response = self.chat(content, system_prompt=system_prompt)
        while response and rounds <= max_rounds:
            response = self.process_reply(response)
            rounds += 1
            if self.is_stopped():
                # 检查任务是否被外部停止
                self.log.info("Task stopped")
                break

        # 打印任务总结、自动保存并响铃提示
        self.print_summary()
        self._auto_save()
        self.console.bell()
        self.log.info("Loop done", rounds=rounds)

    def done(self) -> None:
        """任务完成处理"""
        # 检查任务是否已开始
        if not self.instruction or not self.start_time:
            self.log.warning("Task not started, skipping save")
            return

        # 记录完成时间并尝试自动保存
        self.done_time = time.time()
        if not self.saved:
            self.log.warning("Task not saved, trying to save")
            self._auto_save()

        # 恢复原始工作目录并重命名任务目录
        os.chdir(self.manager.cwd)  # 切换回原始工作目录
        curname = self.task_id
        newname = get_safe_filename(self.instruction, extension=None)
        if newname and os.path.exists(curname):
            try:
                os.rename(curname, newname)
            except Exception as e:
                self.log.exception("Error renaming task directory", curname=curname, newname=newname)

        # 打印任务完成信息
        self.log.info("Task done", path=newname)
        self.console.print(f"[green]{T('Result file saved')}: \"{newname}\"")

    def to_record(self) -> "TaskRecord":
        start_time: str = datetime.fromtimestamp(self.start_time).strftime("%H:%M:%S") if self.start_time else "-"
        done_time: str = datetime.fromtimestamp(self.done_time).strftime("%H:%M:%S") if self.done_time else "-"
        return TaskRecord(task_id=self.task_id, start_time=start_time, done_time=done_time, instruction=self.instruction[:32] if self.instruction else "-")

    # endregion

    # region: 输出和显示
    def box(self, title: str, content: str, align: str = None, lang: str = None) -> None:
        if lang:
            content = Syntax(content, lang, line_numbers=True, word_wrap=True)
        else:
            content = Markdown(content)

        if align:
            content = Align(content, align=align)

        self.console.print(Panel(content, title=title))

    def print_code_result(self, block: CodeBlock, result: Dict[str, Any], title: str = None) -> None:
        line_numbers = True if "traceback" in result else False
        syntax_code = Syntax(block.code, block.lang, line_numbers=line_numbers, word_wrap=True)
        json_result = json.dumps(result, ensure_ascii=False, indent=2, default=str)
        syntax_result = Syntax(json_result, "json", line_numbers=False, word_wrap=True)
        group = Group(syntax_code, Rule(), syntax_result)
        panel = Panel(group, title=title or block.name)
        self.console.print(panel)

    def print_summary(self, detail: bool = False) -> None:
        history = self.client.history
        if detail:
            table = Table(title=T("Task Summary"), show_lines=True)

            table.add_column(T("Round"), justify="center", style="bold cyan", no_wrap=True)
            table.add_column(T("Time(s)"), justify="right")
            table.add_column(T("In Tokens"), justify="right")
            table.add_column(T("Out Tokens"), justify="right")
            table.add_column(T("Total Tokens"), justify="right", style="bold magenta")

            round = 1
            for row in history.get_usage():
                table.add_row(
                    str(round),
                    str(row["time"]),
                    str(row["input_tokens"]),
                    str(row["output_tokens"]),
                    str(row["total_tokens"]),
                )
                round += 1
            self._console.print("\n")
            self._console.print(table)

        summary = history.get_summary()
        summary["elapsed_time"] = time.time() - self.start_time
        summarys = "| {rounds} | {time:.3f}s/{elapsed_time:.3f}s | Tokens: {input_tokens}/{output_tokens}/{total_tokens}".format(**summary)
        event_bus.broadcast("summary", summarys)
        self.console.print(f"\n⏹ [cyan]{T('End processing instruction')} {summarys}")

    # endregion

    # region: LLM交互与消息处理和解析
    def chat(self, context: LLMContext, *, system_prompt=None) -> str | None:
        quiet = self.settings.gui and not self.settings.debug
        msg: ChatMessage = self.client(context, system_prompt=system_prompt, quiet=quiet)
        if msg.role == "error":
            self.console.print(f"[red]{msg.content}[/red]")
            return None
        if msg.reason:
            content = f"{msg.reason}\n\n-----\n\n{msg.content}"
        else:
            content = msg.content
        self.box(f"[yellow]{T('Reply')} ({self.client.name})", content)
        return msg.content

    def process_reply(self, markdown: str) -> None | str:
        """处理用户输入的Markdown文本"""
        #
        # self.console.print(f"{T('Start parsing message')}...", style='dim white')
        parse_mcp = self.mcp is not None
        ret = self.code_blocks.parse(markdown, parse_mcp=parse_mcp)
        if not ret:
            return None

        json_str = json.dumps(ret, ensure_ascii=False, indent=2, default=str)
        self.box(f"✅ {T('Message parse result')}", json_str, lang="json")

        if "call_tool" in ret:
            # 有可能MCP调用会按照代码格式返回，这种情况下存在exec_blocks和call_tool两个键,也可能会有erros字段
            # 优先处理call_tool
            return self.process_mcp_reply(ret["call_tool"])

        errors = ret.get("errors")
        if errors:
            event_bus("result", errors)
            self.console.print(f"{T('Start sending feedback')}...", style="dim white")
            feed_back = f"# 消息解析错误\n{json_str}"
            ret: str | None = self.chat(feed_back)
        elif "exec_blocks" in ret:
            ret = self.process_code_reply(ret["exec_blocks"])
        else:
            ret = None
        return ret

    def process_code_reply(self, exec_blocks: List[CodeBlock]) -> str | None:
        results = []
        for block in exec_blocks:
            event_bus("exec", block)
            self.console.print(f"⚡ {T('Start executing code block')}: {block.name}", style="dim white")
            result = self.runner(block)
            self.print_code_result(block, result)
            result["block_name"] = block.name
            results.append(result)
            event_bus("result", result)

        msg = get_results_prompt(results)
        self.console.print(f"{T('Start sending feedback')}...", style="dim white")
        feed_back = json.dumps(msg, ensure_ascii=False, default=str)
        return self.chat(feed_back)

    def process_mcp_reply(self, json_content: str) -> str | None:
        """处理 MCP 工具调用的回复"""
        # 构造一个包含内容和语言类型的 block ，并触发 tool_call 事件
        block: Dict[str, str] = {"content": json_content, "language": "json"}
        event_bus("tool_call", block)
        self.console.print(f"⚡ {T('Start calling MCP tool')} ...", style="dim white")

        # 解析 json_content，调用 MCP 工具
        call_tool: Dict[str, Any] = json.loads(json_content)
        result: Dict[str, Any] = self.mcp.call_tool(call_tool["name"], call_tool.get("arguments", {}))
        event_bus("result", result)
        # 构造 CodeBlock 用于展示调用内容
        code_block: CodeBlock = CodeBlock(code=json_content, lang="json", name=call_tool.get("name", "MCP Tool Call"), version=1)
        self.print_code_result(code_block, result, title=T("MCP tool call result"))

        # 发送反馈消息
        self.console.print(f"{T('Start sending feedback')}...", style="dim white")
        msg = get_mcp_result_prompt(result)
        feed_back = json.dumps(msg, ensure_ascii=False, default=str)
        feedback_response = self.chat(feed_back)
        return feedback_response

    # endregion

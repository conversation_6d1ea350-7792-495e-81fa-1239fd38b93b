import os
from pathlib import Path
from collections import deque
from typing import TYPE_CHECKING, NamedTuple
from loguru import logger

from ..i18n import T
from .task import Task
from .plugin import PluginManager
from .llm import ClientManager
from .config import PLUGINS_DIR, TIPS_DIR, get_mcp_config_file, get_tt_api_key
from .prompt import get_system_prompt
from .tips import TipsManager
from .mcp_tool import MCPToolManager

if TYPE_CHECKING:
    from rich.console import Console
    from .config import Settings
    from .task import TaskRecord

class EnvRecord(NamedTuple):
    Name: str
    Description: str
    Value: str


class TaskManager:
    MAX_TASKS = 16

    def __init__(self, settings: "Settings", console: "Console", gui: bool = False):
        self.settings: Settings = settings
        self.console: Console = console
        self.tasks: deque[Task] = deque(maxlen=self.MAX_TASKS)
        self.envs: "dict[str, EnvRecord]" = {}
        self.gui: bool = gui
        self.log = logger.bind(src="taskmgr")
        self.api_prompt = None
        self.plugin_manager: "PluginManager" = PluginManager(PLUGINS_DIR)
        self.plugin_manager.load_plugins()
        if settings.workdir:
            workdir: Path = Path.cwd() / settings.workdir
            workdir.mkdir(parents=True, exist_ok=True)
            os.chdir(workdir)
            self.cwd = workdir
        else:
            self.cwd = Path.cwd()
        self._init_environ()
        self.tt_api_key = get_tt_api_key(settings)
        # 始终初始化MCPToolManager，内置工具也需要它
        mcp_config_file = get_mcp_config_file(settings.get("_config_dir"))
        self.mcp = MCPToolManager(mcp_config_file, self.tt_api_key)
        self._init_api()
        self.client_manager: "ClientManager" = ClientManager(settings)
        self.tips_manager: "TipsManager" = TipsManager(TIPS_DIR)
        self.tips_manager.load_tips()
        self.tips_manager.use(settings.get("role", "aipy"))
        self.task = None

    def _init_environ(self):
        envs = self.settings.get("environ", {})
        for name, value in envs.items():
            os.environ[name] = value

    def _init_api(self):
        api = self.settings.get("api", {})

        lines = []
        for api_name, api_conf in api.items():
            lines.append(f"## {api_name} API")
            desc = api_conf.get("desc")
            if desc:
                lines.append(f"### API {T('Description')}\n{desc}")

            envs = api_conf.get("env")
            if not envs:
                continue

            lines.append(f"### {T('Environment variable name and meaning')}")
            for name, (value, desc) in envs.items():
                value = value.strip()
                if not value:
                    continue
                lines.append(f"- {name}: {desc}")
                self.envs[name] = (value, desc)

        self.api_prompt = "\n".join(lines)

    @property
    def workdir(self) -> str:
        return str(self.cwd)

    # region: 任务管理相关
    def new_task(self) -> Task:
        if self.task:
            task = self.task
            self.task = None
            self.log.info("Reload task", task_id=task.task_id)
            return task

        with_mcp = self.settings.get("mcp", {}).get("enable", True)

        mcp_tools = ""
        if self.mcp and with_mcp:
            mcp_tools = self.mcp.get_tools_prompt()
        system_prompt = get_system_prompt(self.tips_manager.current_tips, self.api_prompt, self.settings.get("system_prompt"), mcp_tools=mcp_tools)

        task = Task(self)
        task.client = self.client_manager.Client()
        task.system_prompt = system_prompt
        task.mcp = self.mcp if with_mcp else None
        self.tasks.append(task)
        self.log.info("New task created", task_id=task.task_id)
        return task

    def get_tasks(self) -> list[Task]:
        return list(self.tasks)

    def list_tasks(self)  -> "list[TaskRecord]":
        rows: "list[TaskRecord]" = []
        for task in self.tasks:
            rows.append(task.to_record())
        return rows

    def get_task_by_id(self, task_id):
        for task in self.tasks:
            if task.task_id == task_id:
                return task
        return None

    # endregion

    # region: LLM/角色/任务切换相关
    def use(self, llm=None, role=None, task=None) -> None:
        if llm:
            ret = self.client_manager.use(llm)
            self.console.print(f"LLM: {'[green]Ok[/green]' if ret else '[red]Error[/red]'}")
        if role:
            ret = self.tips_manager.use(role)
            self.console.print(f"Role: {'[green]Ok[/green]' if ret else '[red]Error[/red]'}")
        if task:
            task = self.get_task_by_id(task)
            self.console.print(f"Task: {'[green]Ok[/green]' if task else '[red]Error[/red]'}")
            self.task = task

    def list_llms(self):  # -> list:
        return self.client_manager.to_records()

    # endregion

    def list_envs(self) -> list[EnvRecord]:
        # EnvRecord = namedtuple("EnvRecord", ["Name", "Description", "Value"])
        rows = []
        for name, (value, desc) in self.envs.items():
            rows.append(EnvRecord(name, desc, value[:32]))
        return rows


if __name__ == "__main__":
    from rich.console import Console
    from dynaconf import Dynaconf

    # 创建一个简化的配置对象
    settings = Dynaconf(settings_files=[], envvar_prefix="AIPY", merge_enabled=True)

    # 设置基本配置
    settings.update(
        {
            "workdir": "work",
            "share_result": True,
            "auto_install": True,
            "gui": False,
            "debug": False,
            "llm": {"trustoken": {"type": "trust", "api_key": "sk-78wRYZ4QusHiMiEzrUBvD6w5VGkae2PBT4XOzH1rkGmPSjBC", "enable": True}},
            "mcp": {"enable": True},
        }
    )
    print(settings)

    # tm = TaskManager(settings, Console(record=True))
    # task = tm.new_task() 
    # print(tm.get_tasks()[0].task_id)
    # task.run("@/Users/<USER>/Pictures/pig.png 描述一下这个图片")

"""
🔧 核心架构
interface.py 抽象接口定义，提供Runtime、ConsoleInterface、Stoppable等基类
config.py 配置管理，支持TOML格式，API Key验证，动态配置加载
🚗 任务执行
task.py 任务执行核心，代码块解析执行，结果保存展示
taskmgr.py 任务管理器，任务队列、环境变量、插件管理

🧠 AI/LLM核心
llm.py 大语言模型客户端管理，多模型支持，流式响应处理
multimodal.py 多模态内容处理，支持文本、图片、文件的统一解析
📝 提示词和内容
prompt.py 提示词管理，系统提示词、任务模板、聊天处理
tips.py 提示词库管理，模板加载、角色提示词

💻 代码执行
blocks.py 代码块管理，解析提取、版本管理、依赖关系
runtime.py 运行时环境，Python代码执行、包安装管理
🛠️ 工具和扩展
mcp_tool.py MCP工具管理，服务器连接、工具调用、状态管理
libmcp.py MCP协议库，客户端实现、配置读取、工具调用解析
tool.py 通用工具函数集合

🔌 插件和事件
plugin.py 插件系统，事件总线、插件管理器、加载机制
💾 存储和缓存
cache.py 缓存管理，数据缓存机制、缓存策略
trustoken.py 信任令牌管理，API密钥、安全认证
🎯 辅助功能
wizard.py 向导功能，用户引导和帮助
utils.py 工具函数，通用工具函数集合

"""

from .taskmgr import TaskManager
from .plugin import event_bus
from .config import ConfigManager, CONFIG_DIR

__all__ = ['TaskManager', 'event_bus', 'ConfigManager', 'CONFIG_DIR']

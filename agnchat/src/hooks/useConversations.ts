import { useCallback, useEffect, useState } from 'react';

/**
 * @description 会话类型定义
 */
export type Conversation = { id: string };

/**
 * @description 管理会话列表、当前会话状态以及相关操作的自定义 Hook
 */
export function useConversations() {
  // 存储会话列表
  const [conversations, setConversations] = useState<Conversation[]>([]);
  // 存储当前选中的会话 ID
  const [currentId, setCurrentId] = useState<string | null>(null);

  /**
   * @description 从服务器获取会话列表并更新状态
   * - 如果列表为空，则自动创建一个新会话。
   * - 如果列表不为空，则尝试从 localStorage 恢复上次选中的会话 ID。
   * - 如果 localStorage 中没有或无效，则默认选中最后一个会话。
   * @returns {Promise<string>} 返回最终确定的会话 ID。
   */
  const fetchList = useCallback(async () => {
    const res = await fetch('/api/conversations');
    const list = await res.json();
    setConversations(list);
    if (list.length === 0) {
      // 如果没有对话，自动创建一个新对话
      console.log("没有找到对话，自动创建新对话");
      const newRes = await fetch('/api/conversation', { method: 'POST' });
      const newData = await newRes.json();
      setCurrentId(newData.id);
      localStorage.setItem('agnflow_conversation_id', newData.id);
      // 重新获取列表以更新状态
      const updatedRes = await fetch('/api/conversations');
      const updatedList = await updatedRes.json();
      setConversations(updatedList);
      return newData.id;
    } else {
      const localId = localStorage.getItem('agnflow_conversation_id');
      const found = list.find((c: any) => c.id === localId);
      if (localId && found) {
        setCurrentId(localId);
        return localId;
      } else {
        const last = list[list.length - 1];
        setCurrentId(last.id);
        localStorage.setItem("agnflow_conversation_id", last.id);
        return last.id;
      }
    }
  }, []);

  /**
   * @description 创建一个新的会话
   * - 向服务器发送 POST 请求以创建会话。
   * - 创建成功后，刷新会话列表，并将会话设置为当前会话。
   * @returns {Promise<string>} 返回新创建的会话 ID。
   */
  const createConversation = useCallback(async () => {
    const res = await fetch('/api/conversation', { method: 'POST' });
    const data = await res.json();
    await fetchList();
    setCurrentId(data.id);
    localStorage.setItem('agnflow_conversation_id', data.id);
    return data.id;
  }, [fetchList]);

  /**
   * @description 删除指定的会话
   * - 删除后刷新会话列表。
   * - fetchList 会自动处理没有会话的情况（创建新会话）。
   * @param {string} id - 要删除的会话 ID。
   */
  const deleteConversation = useCallback(async (id: string) => {
    await fetch(`/api/conversation/${id}`, { method: 'DELETE' });
    // 删除后重新获取列表，fetchList 会自动处理空列表的情况
    const newId = await fetchList();
    setCurrentId(newId);
  }, [fetchList]);

  /**
   * @description 切换到指定的会话
   * @param {string} id - 要切换到的会话 ID。
   */
  const switchConversation = useCallback((id: string) => {
    setCurrentId(id);
    localStorage.setItem('agnflow_conversation_id', id);
  }, []);

  // 组件挂载时，自动获取会话列表并设置初始会话
  useEffect(() => {
    (async () => {
      const id = await fetchList();
      // fetchList 现在总是返回一个有效的 ID（如果没有会话会自动创建）
      setCurrentId(id);
    })();
  }, [fetchList]);

  return {
    conversations,
    currentId,
    createConversation,
    deleteConversation,
    switchConversation,
    fetchList,
    setCurrentId,
  };
} 
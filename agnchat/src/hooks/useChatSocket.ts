import {useEffect, useRef, useState, useCallback} from "react";
import type {ChatOptions} from "../types/chat";

// 聊天消息的类型定义
export type ChatMessage = {
  id: string; // 消息的唯一标识
  role: "user" | "ai"; // 消息发送者的角色：用户或 AI
  content: string; // 消息内容
  timestamp?: string; // 消息时间戳 (可选)
};

// WebSocket 连接状态的类型定义
export type WSStatus = "connecting" | "connected" | "disconnected" | "streaming";

/**
 * 管理与聊天服务器的 WebSocket 连接，处理消息的发送、接收和状态管理。
 */
export function useChatSocket(conversationId: string | null) {
  // 存储聊天消息列表
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  // WebSocket 连接状态
  const [status, setStatus] = useState<WSStatus>("connecting");
  // WebSocket 实例的引用
  const wsRef = useRef<WebSocket | null>(null);
  // 是否正在接收 AI 的流式响应
  const [isStreaming, setIsStreaming] = useState(false);
  // 当前正在流式传输的 AI 消息
  const [streamingAiMessage, setStreamingAiMessage] = useState<ChatMessage | null>(null);
  // 当前流式 AI 消息的 ID 引用
  const aiMsgIdRef = useRef<string | null>(null);

  // useEffect 用于处理 WebSocket 的连接和消息处理
  useEffect(() => {
    // 如果没有对话 ID，则设置为等待状态
    // if (!conversationId) {
    //   setStatus("connecting"); // 保持连接中状态，等待对话ID
    //   setMessages([]);
    //   setStreamingAiMessage(null);
    //   return;
    // }

    // 初始化状态
    setStatus("connecting");
    setMessages([]);
    setStreamingAiMessage(null);

    // 创建 WebSocket 连接
    const ws = new WebSocket((window.location.protocol === "https:" ? "wss://" : "ws://") + window.location.host + "/ws");
    wsRef.current = ws;

    // 连接建立时的回调
    ws.onopen = () => {
      setStatus("connected");
      // 发送对话 ID 到服务器以加入对话
      ws.send(JSON.stringify({conversation: conversationId}));
    };

    // 收到消息时的回调
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === "history") {
        // 处理历史消息
        // 修正：将后端的 'assistant' 角色映射为前端的 'ai' 角色
        const mappedRole = data.role === "assistant" ? "ai" : "user";
        setMessages((msgs) => [...msgs, {...data, role: mappedRole}]);
      } else if (data.type === "start") {
        // 开始接收流式消息
        setIsStreaming(true);
        setStatus("streaming");
        aiMsgIdRef.current = data.id || Math.random().toString(36).slice(2);
        // 初始化流式消息对象
        setStreamingAiMessage({id: aiMsgIdRef.current || Math.random().toString(36).slice(2), role: "ai", content: ""});
      } else if (data.type === "chunk") {
        // 接收到流式消息的数据块
        setStreamingAiMessage((msg) => (msg ? {...msg, content: msg.content + data.content} : null));
      } else if (data.type === "end") {
        // 流式消息接收完毕
        setIsStreaming(false);
        setStatus("connected");
        aiMsgIdRef.current = null;
      }
    };

    // 连接关闭时的回调
    ws.onclose = () => {
      setStatus("disconnected");
    };

    // 组件卸载时关闭 WebSocket 连接
    return () => {
      ws.close();
    };
  }, [conversationId]); // 依赖于 conversationId，当它变化时重新建立连接

  // useEffect 用于在流式响应结束后，将完整的 AI 消息添加到消息列表中
  useEffect(() => {
    if (!isStreaming && streamingAiMessage) {
      setMessages((msgs) => [...msgs, {...streamingAiMessage}]);
      setStreamingAiMessage(null); // 清空当前的流式消息
    }
  }, [isStreaming, streamingAiMessage]);

  /**
   * 发送消息到 WebSocket 服务器
   * @param content - 消息文本内容
   * @param options - 聊天选项，如 `entry_action`
   * @param agentConfigs - 不同 agent 的特定配置
   * @param images - 图片数据列表 (DataURL)
   */
  const sendMessage = useCallback((content: string, options: ChatOptions, agentConfigs?: Record<string, any>, images?: string[]) => {
    // 确保 WebSocket 已连接
    if (!wsRef.current || wsRef.current.readyState !== 1) return;

    // 调试日志：准备发送的消息数据
    console.log("📤 准备发送消息:", {
      content,
      options,
      agentConfigs,
      hasImages: images?.length > 0,
    });

    // 构造要发送的基础消息对象
    const message: any = {type: "message", content, options, entry_action: options.entry_action, agent_config: agentConfigs?.[options.entry_action || ""] || {}};

    // 如果是视觉分析节点 (vision_node) 并且有图片，则特殊处理
    if (options.entry_action === "vision_node" && images?.length > 0) {
      console.log("🖼️ 处理视觉分析节点的图片数据");
      message.agent_config = {image_path: images[0]};
      // 调试日志：打印分析类型和图片数据长度
      console.log("  - 分析类型:", message.agent_config.query_type);
      console.log("  - 图片数据长度:", images[0].length);
    }

    // 调试日志：打印最终发送的消息结构
    console.log("📨 发送最终消息:", {
      type: message.type,
      content,
      entry_action: message.entry_action,
      agent_config: {
        ...message.agent_config,
        image_path: message.agent_config.image_path ? `[图片数据长度: ${message.agent_config.image_path.length}]` : undefined,
      },
    });

    // 通过 WebSocket 发送 JSON 格式的消息
    wsRef.current.send(JSON.stringify(message));
    // 将用户发送的消息立即添加到消息列表中，以实现即时反馈
    setMessages((msgs) => [...msgs, {id: Math.random().toString(36).slice(2), role: "user", content}]);
  }, []);

  /**
   * 删除指定 ID 的消息
   * @param id - 要删除的消息 ID
   */
  const deleteMessage = useCallback(async (id: string) => {
    // 向后端 API 发送删除请求
    await fetch(`/api/message/${id}`, {method: "DELETE"});
    // 从前端状态中移除该消息
    setMessages((msgs) => msgs.filter((m) => m.id !== id));
  }, []);

  /**
   * 清空当前对话的所有消息
   */
  const clearMessages = useCallback(() => setMessages([]), []);

  // 返回 Hook 的公共接口
  return {
    messages,
    status,
    isStreaming,
    streamingAiMessage,
    sendMessage,
    deleteMessage,
    clearMessages,
  };
}

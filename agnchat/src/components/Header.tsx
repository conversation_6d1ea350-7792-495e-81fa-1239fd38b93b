import React, {useState} from "react";
import "../styles/Header.css";

// Header 组件：顶部栏，包含窗口按钮、主题切换、标题、会话操作等
// Header 组件props定义
interface HeaderProps {
  onNewConv: () => void; // 新建会话回调
  onSwitchConv: () => void; // 切换会话回调
  onClearHistory: () => void; // 清空历史回调
  status: string; // 状态文本
  onMaximize: () => void; // 最大化窗口回调
  onMinimize: () => void; // 最小化窗口回调
}

const Header: React.FC<HeaderProps> = ({onNewConv, onSwitchConv, onClearHistory, status, onMaximize, onMinimize}) => {
  // hovered：窗口按钮悬停状态
  const [hovered, setHovered] = useState(false);
  // isDarkMode：当前是否为暗色模式
  const [isDarkMode, setIsDarkMode] = useState(true); // 默认黑暗模式

  // 主题色列表
  const themeColors = ["#00B4DB", "#00ffe7", "#FFD93D", "#43E97B", "#ffb3b3", "#c3b6f7", "#FF8C42"];
  // themeIndex：当前主题色索引
  const [themeIndex, setThemeIndex] = useState(0);
  // 切换主题色
  const handleThemeSwitch = () => {
    const nextIndex = (themeIndex + 1) % themeColors.length;
    setThemeIndex(nextIndex);
    document.documentElement.style.setProperty("--main-color", themeColors[nextIndex]);
  };

  // 切换明暗模式
  const handleThemeToggle = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);

    if (newMode) {
      // 切换到黑暗模式
      document.documentElement.style.setProperty("--bg-color", "var(--bg-dark-color)");
      document.documentElement.style.setProperty("--text-color", "var(--text-dark-color)");
      document.documentElement.classList.remove("light-mode");
      document.documentElement.style.setProperty("--code-color", "var(--code-dark-color)");
    } else {
      // 切换到亮色模式
      document.documentElement.style.setProperty("--bg-color", "var(--bg-light-color)");
      document.documentElement.style.setProperty("--text-color", "var(--text-light-color)");
      document.documentElement.classList.add("light-mode");
      document.documentElement.style.setProperty("--code-color", "var(--code-light-color)");
    }
  };

  return (
    <div className="header">
      {/* 窗口按钮+主题切换 */}
      <div className="mac-buttons" onMouseEnter={() => setHovered(true)} onMouseLeave={() => setHovered(false)}>
        <button className={`mac-circle-btn minimize${hovered ? " hovered" : ""}`} title="最小化" onClick={onMinimize}>
          <span>➖</span>
        </button>
        <button className={`mac-circle-btn maximize${hovered ? " hovered" : ""}`} title="最大化" onClick={onMaximize}>
          <span>➕</span>
        </button>
        <button className={`mac-circle-btn theme${hovered ? " hovered" : ""}`} title="切换主题色" onClick={handleThemeSwitch}>
          <span>{"</>"}</span>
        </button>
      </div>
      {/* 标题和状态 */}
      <h1 title="AgnFlow 聊天室">
        🤖 AgnFlow 聊天室{" "}
        <span className="status" id="status">
          {status}
        </span>
      </h1>
      {/* 会话管理功能按钮，以及明暗模式切换 */}
      <div className="header-controls">
        <div className="header-buttons">
          {/* 新建、切换、清空会话按钮 */}
          <button id="newConvBtn" className="header-btn" onClick={onNewConv}>
            💬 新增对话
          </button>
          <button id="switchConvBtn" className="header-btn" onClick={onSwitchConv}>
            🔄 切换对话
          </button>
          <button id="clearHistoryBtn" className="header-btn" onClick={onClearHistory}>
            🗑️ 清空对话
          </button>
        </div>
        <div className="theme-controls">
          {/* 明暗模式切换按钮 */}
          <button className={`theme-toggle-btn ${isDarkMode ? "dark" : "light"}`} onClick={handleThemeToggle} title={isDarkMode ? "切换到亮色模式" : "切换到黑暗模式"}>
            {isDarkMode ? "☀️" : "🌙"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Header;

import React from "react";

// ConversationModal 组件：会话列表弹窗，支持切换、删除、关闭会话
// 会话类型定义
export type Conversation = {id: string};

// ConversationModal 组件props定义
const ConversationModal: React.FC<{
  open: boolean; // 是否显示弹窗
  conversations: Conversation[]; // 会话列表
  currentId: string | null; // 当前会话ID
  onSwitch: (id: string) => void; // 切换会话回调
  onDelete: (id: string) => void; // 删除会话回调
  onClose: () => void; // 关闭弹窗回调
}> = ({open, conversations, currentId, onSwitch, onDelete, onClose}) => {
  if (!open) return null; // 未打开时不渲染
  return (
    <div style={{position: "fixed", left: 0, top: 0, width: "100vw", height: "100vh", background: "rgba(0,0,0,0.3)", zIndex: 1000}} onClick={onClose}>
      <div id="convListPanel" onClick={(e) => e.stopPropagation()}>
        <h3 style={{color: "var(--main-color)", marginBottom: 12}}>会话列表</h3>
        {/* 无会话时提示 */}
        {conversations.length === 0 && <div style={{color: "#fff"}}>暂无会话</div>}
        {/* 会话列表渲染，最新在前 */}
        <ul className="scrollbar-thin" style={{listStyle: "none", padding: 0, margin: 0, maxHeight: 300, overflow: "auto"}}>
          {[...conversations].reverse().map((conv) => (
            <li key={conv.id} style={{marginBottom: 10, display: "flex", alignItems: "center"}}>
              {/* 切换会话按钮，当前高亮 */}
              <button style={{flex: 1, textAlign: "left", background: conv.id === currentId ? "var(--main-color)" : "#101c2c", color: conv.id === currentId ? "#232526" : "var(--main-color)", border: "none", padding: "8px 12px", borderRadius: 8, cursor: "pointer", fontWeight: 600}} onClick={() => onSwitch(conv.id)}>
                {conv.id.slice(0, 8)}...{conv.id.slice(-4)}
              </button>
              {/* 删除会话按钮 */}
              <button style={{marginLeft: 8, background: "#ff3b3b", color: "#fff", border: "none", padding: "6px 10px", borderRadius: 6, cursor: "pointer"}} onClick={() => onDelete(conv.id)}>
                删除
              </button>
            </li>
          ))}
        </ul>
        {/* 关闭弹窗按钮 */}
        <div style={{textAlign: "right", marginTop: 10}}>
          <button id="closeConvList" style={{background: "#232526", color: "var(--main-color)", border: "1.5px solid var(--main-color)", borderRadius: 8, padding: "6px 18px", cursor: "pointer"}} onClick={onClose}>
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConversationModal;

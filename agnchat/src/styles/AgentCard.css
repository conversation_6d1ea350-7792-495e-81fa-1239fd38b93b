/* 智能体卡片主容器 */
.agent-card {
  position: relative;
  width: 400px;
  background: var(--bg-color);
  border-radius: 18px;
  /* 主色阴影和内描边 */
  box-shadow: 0 2px 8px var(--main-color), 0 0 0 2px var(--main-color) inset;
  border: 1.5px solid var(--main-color);
  padding: 0 32px 24px 32px;
  margin: 16px;
  transition: all 0.3s;
  color: var(--text-color);
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 选中态样式 */
.agent-card.selected {
  border: 2px solid var(--primary-color, #4a90e2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.2);
}

/* 编辑态样式 */
.agent-card.editing {
  width: 400px;
  cursor: default;
}

/* 卡片头部布局 */
.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  position: sticky;
  top: 0;
  background: var(--bg-color);
  z-index: 2;
  padding-top: 24px;
  padding-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card-header h3 {
  flex: 1;
  font-size: 18px;
  color: var(--text-color);
  margin: 0;
  /* 主色描边发光 */
  text-shadow: 0 0 2px var(--main-color), 0 0 6px var(--main-color);
  padding-bottom: 4px;
}

.card-header .example-btn {
  margin-left: 12px;
}


.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
  flex: 1 1 auto;
  min-height: 0;
  padding-bottom: 8px;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: var(--main-color, #4a90e2) var(--bg-color, #181c20);
}
.card-content::-webkit-scrollbar {
  width: 8px;
  background: var(--bg-color, #181c20);
  border-radius: 8px;
}
.card-content::-webkit-scrollbar-thumb {
  background: var(--main-color, #4a90e2);
  border-radius: 8px;
  min-height: 24px;
  transition: background 0.2s;
}
.card-content::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color, #357ae8);
}

/* 表单字段整体布局 */
.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 字段名样式，主色+下划线 */
.form-field label {
  color: var(--main-color, #666);
  font-size: 14px;
  padding-bottom: 2px;
  display: inline-block;
  margin-bottom: 2px;
}
.form-field label:first-of-type {
  border-bottom: 2px solid var(--main-color, #4a90e2);
  /* 不换行 */
  white-space: nowrap;
}

.field {
  position: relative;
}

/* 输入框、文本域、下拉框主色描边 */
.field input[type="number"],
.field input[type="text"], 
.field textarea,
.select-field select {
  width: 100%;
  padding: 8px 12px;
  border: 1.5px solid var(--main-color);
  border-radius: 6px;
  font-size: 14px;
  box-shadow: 0 0 4px var(--main-color) inset;
  transition: border 0.2s, box-shadow 0.2s;
}


.field input[type="text"]:focus,
.field textarea:focus,
.select-field select:focus {
  border: 2px solid var(--main-color);
  box-shadow: 0 0 8px var(--main-color) inset;
}


.field textarea {
  min-height: 100px;
  font-family: monospace;
  resize: vertical;
}

/* 单选组布局 */
.radio-group {
  display: flex;
  gap: 12px;
}

.radio-group label {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

/* 字段操作按钮组 */
.field-actions {
  display: flex;
  gap: 8px;
  margin-top: 0;
  align-items: center;
}

.field-actions button {
  background: var(--bg-color);
  color: var(--main-color);
  border: 1.5px solid var(--main-color);
  border-radius: 8px;         /* 更小的圆角 */
  font-weight: 600;
  font-size: 13px;
  padding: 5px 16px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  box-shadow: 0 1px 4px var(--main-color, #4a90e2)22;
}

.field-actions button:hover {
  background: var(--main-color);
  color: var(--text-color, #fff);
}
/* 卡片内容摘要区 */
.card-summary {
  color: var(--main-color, #666);
}

.card-summary .type {
  font-size: 14px;
  color: var(--primary-color, #4a90e2);
  margin: 0 0 8px;
  /* 主色描边+左侧主色条 */
  text-shadow: 0 0 2px var(--main-color);
  border-left: 4px solid var(--main-color);
  padding-left: 8px;
}

.card-summary .description {
  font-size: 14px;
  margin: 0 0 12px;
  line-height: 1.5;
}

/* 标签组 */
.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 单个标签主色渐变+描边 */
.tag {
  padding: 2px 8px;
  background: var(--tag-bg, #f0f0f0);
  border-radius: 12px;
  font-size: 12px;
  color: var(--main-color, #666);
  border: 1.5px solid var(--main-color);
  box-shadow: 0 0 4px var(--main-color) inset;
  background: linear-gradient(90deg, var(--main-color) 10%, #fff 100%);
  color: var(--main-color);
  font-weight: bold;
}
/* 文本域字段布局 */
.textarea-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* JSON字段布局 */
.json-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 多选组左右结构 */
.checkbox-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

/* 下拉选择字段布局 */
.select-field {
  display: flex;
  align-items: center;
  gap: 8px;
}

.select-field select {
  padding: 8px 12px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 6px;
  font-size: 14px;
  background: var(--card-bg, #fff);
  color: var(--text-primary, #333);
}

/* 示例按钮主色风格 */
.agent-card .example-btn {
  background: var(--bg-color);
  color: var(--main-color);
  border: 1.5px solid var(--main-color);
  border-radius: 18px;
  font-weight: 600;
  font-size: 14px;
  padding: 6px 18px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  box-shadow: 0 2px 8px var(--main-color);
}
.agent-card .example-btn:hover {
  background: var(--main-color);
  color: var(--text-color);
}

/* 蒙层样式 */
.agent-modal-overlay {
  background: var(--modal-bg, rgba(0, 0, 0, 0.3));
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 智能体按钮组，便于定位加号 */
.agent-btn-group {
  position: relative;
  display: flex;
  align-items: center;
  z-index: 1000;  /* 提高层级 */
  overflow-x: visible;
}

/* 编辑加号按钮 */
.agent-edit-btn {
  position: absolute;
  /* overflow-y: auto; */
  top: -10px;
  right: -10px;
  z-index: 9999; 
  background: var(--bg-color);
  color: var(--main-color);
  border: 2px solid var(--main-color);
  border-radius: 50%;
  width: 21px;
  height: 21px;
  font-size: 18px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(229,57,53,0.15);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}
.agent-edit-btn:hover {
  background: var(--main-color);
  color: #fff;
  box-shadow: 0 4px 16px rgba(229,57,53,0.25);
}

/* 文件上传美化 */
.file-upload-field {
  margin-top: 8px;
}
.file-upload-area {
  border: 2px dashed var(--main-color, #4a90e2);
  border-radius: 12px;
  padding: 18px 0;
  text-align: center;
  background: var(--bg-color, #181c20);
  color: var(--main-color, #4a90e2);
  cursor: pointer;
  margin-bottom: 12px;
  transition: border-color 0.2s, background 0.2s;
}
.file-upload-area:focus,
.file-upload-area:hover {
  border-color: var(--primary-color, #4a90e2);
  background: #232b36;
}
.file-upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 24px;
  background: var(--main-color, #4a90e2);
  color: #fff !important;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  border: none;
  margin: 0 auto;
  min-width: 120px;
  min-height: 40px;
  box-sizing: border-box;
  letter-spacing: 1px;
}
.file-upload-btn:hover {
  background: var(--primary-color, #357ae8);
  color: #fff;
}
.file-preview-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;
}
.file-preview-item {
  position: relative;
  background: #232b36;
  border: 1.5px solid var(--main-color, #4a90e2);
  border-radius: 10px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
  max-width: 120px;
  min-height: 80px;
  box-shadow: 0 2px 8px rgba(74,144,226,0.08);
}
.file-preview-img {
  max-width: 80px;
  max-height: 80px;
  border-radius: 6px;
  margin-bottom: 4px;
}
.file-preview-video {
  max-width: 100px;
  max-height: 80px;
  border-radius: 6px;
  margin-bottom: 4px;
}
.file-preview-audio {
  width: 100px;
  margin-bottom: 4px;
}
.file-preview-link {
  color: var(--main-color, #4a90e2);
  font-size: 13px;
  word-break: break-all;
  margin-bottom: 4px;
}
.file-remove-btn {
  position: absolute;
  top: 2px;
  right: 2px;
  background: #e53935;
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}
.file-remove-btn:hover {
  background: #ff5252;
}

/* Switch 切换开关样式 */
.switch-field {
  display: flex;
  align-items: center;
  gap: 16px;
}
.switch-label {
  font-size: 14px;
  color: var(--main-color, #666);
  margin-right: 8px;
  white-space: nowrap;
}
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .3s;
  border-radius: 24px;
  box-shadow: 0 0 4px var(--main-color)22;
}
.switch input:checked + .slider {
  background-color: var(--main-color, #4a90e2);
}
.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .3s;
  border-radius: 50%;
  box-shadow: 0 1px 4px #8882;
}
.switch input:checked + .slider:before {
  transform: translateX(20px);
}

/* 移动端适配 */
@media (max-width: 600px) {
  .agent-card {
    width: 98vw;
    padding: 12px 6vw;
  }
}

/* Header 基础样式 */
.header {
  /* 头部整体内边距和背景渐变，底部主色边框 */
  padding: 24px 20px 16px 20px;
  background: linear-gradient(270deg, var(--main-color) 0%, var(--bg-color) 60%);
  border-bottom: 2px solid var(--main-color);
  text-align: center;
  position: relative;
  z-index: 1;
}

.header h1 {
  font-size: 26px;
  font-family: "Orbitron", "Segoe UI", Arial, sans-serif;
  font-weight: 600;
  color: var(--main-color);
  letter-spacing: 2px;
  text-shadow: 0 0 2px var(--main-color), 0 0 1px #fff;
  margin-bottom: 6px;
  user-select: none;
}

.status {
  font-size: 15px;
  color: var(--text-color);
  font-weight: 500;
  text-shadow: none;
  font-family: "Segoe UI", "Roboto", Arial, sans-serif;
  user-select: none;
}

/* 头部按钮样式 */
.header-btn {
  /* 头部按钮基础样式 */
  padding: 6px 16px;
  background: var(--bg-color);
  color: var(--text-color);
  border: 1.5px solid var(--main-color);
  border-radius: 18px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.header-btn:hover {
  /* 按钮悬停效果 */
  background: var(--main-color);
  color: var(--bg-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 180, 219, 0.3);
}

/* 主题切换按钮 */
.theme-toggle-btn {
  /* 主题切换按钮样式 */
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 1.5px solid var(--main-color);
  background: var(--bg-color);
  color: var(--text-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.2s ease;
}

.theme-toggle-btn:hover {
  /* 主题按钮悬停放大效果 */
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 180, 219, 0.3);
}

.theme-toggle-btn.dark {
  /* 暗色主题按钮背景 */
  background: var(--bg-dark-color);
}

.theme-toggle-btn.light {
  /* 亮色主题按钮背景 */
  background: var(--bg-light-color);
}

/* Mac按钮容器 */
.mac-buttons {
  /* Mac按钮容器定位与排列 */
  position: absolute;
  left: 16px;
  top: 16px;
  display: flex;
  gap: 8px;
  z-index: 10;
}
/* Mac风格窗口按钮 */
.mac-circle-btn {
  /* Mac风格圆形窗口按钮 */
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  transition: all 0.2s ease;
  opacity: 0.5;
  background: var(--bg-color);
}

.mac-circle-btn.minimize {
  /* 最小化按钮颜色 */
  background: #ffbd4c;
}

.mac-circle-btn.maximize {
  /* 最大化按钮颜色 */
  background: #00ca56;
}

.mac-circle-btn.theme {
  /* 主题按钮颜色 */
  background: #00b4db;
}

.mac-circle-btn.hovered {
  /* 鼠标悬停时按钮不透明 */
  opacity: 1;
}

/* Header控制区域 */
.header-controls {
  position: absolute;
  top: 60px;
  left: 24px;
  right: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 2;
  user-select: none;
}

.header-buttons {
  /* 头部按钮组横向排列与滚动隐藏 */
  display: flex;
  gap: 24px;
  overflow-x: auto;
  max-width: 100%;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.theme-controls {
  /* 主题切换按钮组 */
  display: flex;
  gap: 12px;
}

/* 标题样式补充 */
.header h1 {
  /* 标题不可选中 */
  cursor: default;
}


/* 亮色模式适配 */
.light-mode .header {
  /* 亮色模式下头部背景渐变 */
  background: linear-gradient(270deg, var(--main-color) 0%, var(--bg-light-color) 60%);
}

/* 消息列表容器 */
.messages {
  flex: 1;
  overflow-y: auto;
  padding: 24px 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  background: linear-gradient(135deg, var(--bg-color) 0%, #0f2027 100%);
  font-family: "Segoe UI", "Roboto", <PERSON><PERSON>, sans-serif;
}

/* 消息基础样式 */
.message {
  padding: 14px 20px;
  border-radius: 20px;
  font-size: 16px;
  line-height: 1.5;
  word-wrap: break-word;
  border: 1.5px solid var(--main-color);
  position: relative;
  font-family: "Segoe UI", "Roboto", Arial, sans-serif;
  font-weight: 400;
  color: var(--text-color);
  background: var(--bg-color);
  box-shadow: none !important;
  text-shadow: none !important;
  white-space: pre-wrap;
}

.user-message {
  background: var(--main-color);
  color: var(--text-color);
  align-self: flex-end;
  border-bottom-right-radius: 6px;
  border: 1.5px solid var(--main-color);
  font-family: "Segoe UI", "Roboto", <PERSON><PERSON>, sans-serif;
  font-weight: 500;
  box-shadow: none !important;
  text-shadow: none !important;
  white-space: pre-wrap;
}

.ai-message {
  background: var(--bg-color);
  color: var(--text-color);
  align-self: flex-start;
  border-bottom-left-radius: 6px;
  border: 1.5px solid var(--main-color);
  font-family: "Segoe UI", "Roboto", Arial, sans-serif;
  font-weight: 500;
  box-shadow: none !important;
  text-shadow: none !important;
  white-space: pre-wrap;
}

/* 消息删除按钮 */
.msg-delete-btn {
  position: absolute;
  bottom: 4px;
  background: transparent;
  border: none;
  font-size: 18px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
  z-index: 10;
}

.msg-delete-btn:hover {
  opacity: 1;
}

.msg-delete-btn.left {
  left: -28px;
}

.msg-delete-btn.right {
  right: -28px;
}

.msg-content {
  width: 100%;
}

/* 右键菜单样式 */
.zt-context-menu {
  background: var(--bg-color);
  border: 1.5px solid var(--main-color);
  border-radius: 12px;
  box-shadow: 0 2px 8px var(--main-color);
  padding: 6px 0;
  min-width: 120px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.zt-context-menu-item {
  background: transparent;
  color: var(--text-color);
  border: none;
  outline: none;
  font-size: 15px;
  font-weight: 600;
  padding: 10px 18px;
  text-align: left;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
}

.zt-context-menu-item:hover {
  background: linear-gradient(90deg, var(--main-color) 0%, var(--bg-light-color) 100%);
  color: var(--bg-color);
}

/* 推理块样式 */
.reasoning-block {
  margin: 16px 0;
  background: var(--bg-color);
  border: 1.5px solid var(--main-color);
  border-radius: 12px;
  color: var(--text-color);
  font-size: 15px;
  box-shadow: 0 2px 8px var(--main-color);
  padding: 0 0 8px 0;
}

.reasoning-block summary {
  cursor: pointer;
  font-weight: 600;
  font-size: 15px;
  padding: 8px 8px !important;
  outline: none;
  color: var(--text-color);
}

.reasoning-content {
  padding: 10px 18px 0 18px;
  color: var(--text-color);
  font-size: 14px;
  white-space: pre-wrap;
}

.conclusion-block {
  background: var(--bg-color);
  color: var(--text-color);
  font-size: 16px;
  font-weight: 600;
  white-space: pre-wrap;
}

/* 代码块样式 */
.code-block {
  color: var(--code-color);
  border: 1px solid var(--main-color);
  padding: 5px;
  background: var(--bg-color);
  border-radius: 8px;
  font-size: 14px;
  overflow-x: auto;
  white-space: pre;
  max-width: 100%;
}

.code-block .line-number {
  display: inline-block;
  width: 1em;
  color: var(--code-color);
  user-select: none;
  opacity: 0.7;
  text-align: right;
  margin-right: 0.3em;
}

/* 亮色模式适配 */
.light-mode .messages {
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(255, 255, 255, 0.8) 100%);
}

/* 工具调用卡片样式 */
.tool-block {
  background: var(--bg-color);
  border: 2px solid var(--main-color);
  border-radius: 12px;
  margin: 8px 0;
  padding: 6px 14px;
  color: var(--text-color);
  font-size: 15px;
  box-shadow: 0 2px 8px var(--main-color) 22;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.tool-title {
  font-weight: 700;
  font-size: 15px;
  color: #ffd93d;
  margin: 0 8px 0 0;
  flex-shrink: 0;
}
.tool-name {
  color: var(--text-color);
  font-family: "Orbitron", "Segoe UI", Arial, sans-serif;
  font-size: 15px;
  font-weight: 700;
}
.tool-args {
  display: flex;
  gap: 10px;
  margin: 0;
  padding: 0;
}
.tool-arg {
  font-size: 14px;
  margin: 0;
  padding: 0 4px;
  background: var(--bg-color);
  border-radius: 6px;
  border: 1px solid var(--main-color);
  display: flex;
  align-items: center;
}
.tool-arg-key {
  color: #43e97b;
  font-weight: 600;
}
.tool-arg-value {
  color: var(--text-color);
  font-family: "Segoe UI", "Roboto", Arial, sans-serif;
  margin-left: 2px;
}
.tool-set-state {
  color: #ffd93d;
  font-size: 14px;
  font-weight: 600;
  margin-left: 8px;
  flex-shrink: 0;
}
.tool-set-var {
  color: #ff8c42;
  font-family: "Orbitron", "Segoe UI", Arial, sans-serif;
  font-weight: 700;
}


/* 🧩 tool_context 美化样式 */

.tool-context-json {
  font-family: 'JetBrains Mono', 'Menlo', 'Consolas', monospace;
  background: #f3f4f6;
  border-radius: 6px;
  padding: 10px 14px;
  color: #374151;
  font-size: 14px;
  line-height: 1.7;
  margin: 4px 0 0 0;
  white-space: pre;
  overflow-x: auto;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 4px #7c3aed11;
}

@import url("https://fonts.googleapis.com/css2?family=Orbitron:wght@600;700&display=swap");
/* 以下内容完全覆盖，来自原index.html <style> */
:root {
  --main-color: #00b4db;
  --bg-dark-color: #232526; /* 黑暗背景色 */
  --bg-light-color: #eaffff; /* 浅色背景色 */
  --text-light-color: black;
  --text-dark-color: white;
  --code-dark-color: skyblue;
  --code-light-color: green;

  --bg-color: var(--bg-dark-color);
  --text-color: var(--text-dark-color);
  --code-color: var(--code-dark-color);
  /* --bg-color: var(--bg-light-color);
  --text-color: var(--text-light-color); */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body,
#root {
  height: 100% !important;
  width: 100% !important;
  min-height: 100vh !important;
  min-width: 100vw !important;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
}
body {
  font-family: "Segoe UI", "Roboto", Arial, sans-serif !important;
  background: linear-gradient(135deg, #0f2027 0%, #2c5364 100%) !important;
  min-height: 100vh !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 20px !important;
}

.chat-container {
  background: var(--bg-color);
  border-radius: 24px;
  width: 80vw;
  height: 80vh;
  max-width: 90vw;
  max-height: 90vh;
  min-width: 50vh;
  min-height: 50vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 40px 8px var(--main-color), 0 8px 40px rgba(0, 255, 255, 0.08);
  border: 2px solid var(--main-color);
  overflow: hidden;
  position: relative;
  resize: both;
  transition: all 0.3s cubic-bezier(0.4, 2, 0.6, 1);
}

.chat-container:before {
  content: "";
  position: absolute;
  inset: 0;
  pointer-events: none;
  border-radius: 24px;
  box-shadow: 0 0 80px 10px var(--main-color) inset, 0 0 40px 2px #1a2980 inset;
  opacity: 0.15;
}

.code-block {
  color: var(--code-color);
  border: 1px solid var(--main-color);
  padding: 5px;
  background: var(--bg-color);
  border-radius: 8px;
  font-size: 14px;
  overflow-x: auto;
  white-space: pre;
  max-width: 100%;
}

.code-block .line-number {
  display: inline-block;
  width: 1em;
  color: var(--code-color);
  user-select: none;
  opacity: 0.7;
  text-align: right;
  margin-right: 0.3em;
}

.msg-delete-btn {
  position: absolute;
  bottom: 4px;
  background: transparent;
  border: none;
  font-size: 18px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
  z-index: 10;
}
.msg-delete-btn:hover {
  opacity: 1;
}
.msg-delete-btn.left {
  left: -28px;
}
.msg-delete-btn.right {
  right: -28px;
}
.message {
  position: relative;
}

.msg-content {
  width: 100%;
}

#root {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 删除以下 .tool- 相关样式，已迁移到 Messages.css */

#convListPanel {
  position: absolute;
  left: 50%;
  top: 20%;
  transform: translateX(-50%);
  background: var(--bg-color);
  border: 2px solid var(--main-color);
  border-radius: 16px;
  padding: 24px 32px;
  min-width: 320px;
  max-width: 90vw;
  box-shadow: 0 8px 32px var(--main-color);
}

/* 会话列表滚动条样式，和 .messages 区域一致 */
#convListPanel ul {
  max-height: 300px;
  overflow: auto;
}
/* #convListPanel ul::-webkit-scrollbar {
  width: 7px;
}
#convListPanel ul::-webkit-scrollbar-track {
  background: var(--bg-color);
}
#convListPanel ul::-webkit-scrollbar-thumb {
  background: var(--main-color);
  border-radius: 4px;
} */

/* 公共滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: var(--main-color) var(--bg-color);
}
.scrollbar-thin::-webkit-scrollbar {
  width: 7px;
}
.scrollbar-thin::-webkit-scrollbar-track {
  background: var(--bg-color);
}
.scrollbar-thin::-webkit-scrollbar-thumb {
  background: var(--main-color);
  border-radius: 4px;
}

/* 公共卡片样式 */
.card {
  background: var(--bg-color);
  border-radius: 12px;
  box-shadow: 0 2px 8px var(--main-color) 22;
  border: 1.5px solid var(--main-color);
  padding: 16px 24px;
}

/* 公共按钮样式 */
.btn {
  background: #232526;
  color: var(--text-color);
  border: 1.5px solid var(--main-color);
  border-radius: 18px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: background 0.2s, color 0.2s;
}
.btn:hover {
  background: var(--main-color);
  color: var(--text-color);
}

/* 公共字体 */
.font-main {
  font-family: "Segoe UI", "Roboto", Arial, sans-serif;
}

/* 亮色模式专用样式 */
.light-mode .messages {
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.light-mode .input-container {
  background: linear-gradient(90deg, var(--main-color) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.light-mode #sendButton {
  background: linear-gradient(90deg, var(--main-color) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.light-mode #sendButton:hover:not(:disabled) {
  background: linear-gradient(90deg, #1a2980 0%, rgba(255, 255, 255, 0.95) 100%);
}


.light-mode .deep-think-btn.active {
  background: linear-gradient(90deg, var(--main-color) 0%, rgba(255, 255, 255, 0.95) 100%) !important;
}

/* 通用图标按钮样式 */
.icon-btn {
  background: transparent;
  border: 1.5px solid var(--main-color);
  color: var(--main-color);
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  min-height: 40px;
  transition: all 0.2s;
  box-shadow: 0 2px 10px var(--main-color), 0 2px 8px var(--bg-color);
}

.icon-btn:hover:not(:disabled) {
  background: var(--main-color);
  color: var(--text-color);
  transform: scale(1.1);
  box-shadow: 0 4px 20px var(--main-color), 0 2px 8px var(--bg-color);
}

.icon-btn:disabled {
  background: transparent;
  color: #666;
  border: 1.5px solid #666;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
